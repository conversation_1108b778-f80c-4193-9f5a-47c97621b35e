#
# Creates a local docker building environment (alpine & ubuntu).
#

.PHONY: all
all: help

#
# make
#

.ONESHELL:
SHELL = /bin/sh

MAKEFLAGS += --no-print-directory

#
# environment
#

UNAME_M := $(shell uname -m)
UNAME_R := $(shell uname -r)

ifeq ($(UNAME_M),x86_64)
	ARCH = x86_64
	ALTARCH = amd64
endif

ifeq ($(UNAME_M),aarch64)
	ARCH = aarch64
	ALTARCH = arm64
endif

ifeq ($(ALTARCH),)
	@echo "can't find architecture"
	exit 1
endif

#
# tools
#

CMD_DOCKER ?= docker

.check_%:
#
	@command -v $* >/dev/null
	if [ $$? -ne 0 ]; then
		echo "missing required tool $*"
		exit 1
	else
		touch $@ # avoid target rebuilds due to inexistent file
	fi

#
# usage
#

.PHONY: help
help:
	@echo ""
	@echo "CREATES A LOCAL DOCKER BUILDING ENVIRONMENT"
	@echo ""
	@echo "ALPINE:"
	@echo ""
	@echo "  To CREATE an ALPINE building container:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-make alpine-prepare"
	@echo ""
	@echo "  To EXECUTE an ALPINE building container shell:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-make alpine-shell"
	@echo ""
	@echo "UBUNTU:"
	@echo ""
	@echo "  To CREATE an UBUNTU building container:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-make ubuntu-prepare"
	@echo ""
	@echo "  To EXECUTE an UBUNTU building container shell:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-make ubuntu-shell"
	@echo ""
	@echo "NOTES:"
	@echo ""
	@echo "  1. Example of how to use an UBUNTU building env as the \"MAKE\" command:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-make ubuntu-make ARG=\"clean\""
	@echo "    $$ make -f builder/Makefile.tracee-make ubuntu-make ARG=\"bpf\""
	@echo "    $$ make -f builder/Makefile.tracee-make ubuntu-make ARG=\"tracee\""
	@echo ""
	@echo "    > This will generate UBUNTU binaries in the working ./dist directory".
	@echo ""
	@echo "  2. You may provide STATIC variable for static binaries:"
	@echo ""
	@echo "    $$ STATIC=1 make -f builder/Makefile.tracee-make alpine-make ARG=\"tracee\""
	@echo ""
	@echo "    > This will generate a STATIC binary in the working ./dist directory."
	@echo "    > Despite being compiled by ALPINE, it will work in any distro."

	@echo ""

#
# requirements
#

.PHONY: .check_tree
.check_tree:
#
	@if [ ! -d ./builder ]; then
		echo "you must be in the root directory"
		exit 1
	fi

#
# create tracee-make
#

UID = $(shell id -u)
GID = $(shell id -g)
PWD = $(shell pwd)

ALPINE_MAKE_CONTNAME = alpine-tracee-make
ALPINE_MAKE_DOCKERFILE = $(abspath $(shell find -name Dockerfile.alpine-tracee-make))

.PHONY: alpine-prepare
alpine-prepare: \
	| .check_$(CMD_DOCKER) \
	.check_tree
#
	$(CMD_DOCKER) build \
		--network host \
		-f $(ALPINE_MAKE_DOCKERFILE) \
		-t $(ALPINE_MAKE_CONTNAME):latest \
		--build-arg uid=$(UID) \
		--build-arg gid=$(GID) \
		.

UBUNTU_MAKE_CONTNAME = ubuntu-tracee-make
UBUNTU_MAKE_DOCKERFILE = $(abspath $(shell find -name Dockerfile.ubuntu-tracee-make))

.PHONY: ubuntu-prepare
ubuntu-prepare: \
	| .check_$(CMD_DOCKER) \
	.check_tree
#
	$(CMD_DOCKER) build \
		--network host \
		-f $(UBUNTU_MAKE_DOCKERFILE) \
		-t $(UBUNTU_MAKE_CONTNAME):latest \
		--build-arg uid=$(UID) \
		--build-arg gid=$(GID) \
		.

#
# docker environment
#

DOCKER_RUN_ENV =

ifeq ($(STATIC), 1)
    DOCKER_RUN_ENV += -e STATIC=1
endif

ifeq ($(BTFHUB), 1)
    DOCKER_RUN_ENV += -e BTFHUB=1
endif

DOCKER_RUN_ENV += -e RELEASE_VERSION=$(RELEASE_VERSION)

DOCKER_RUN_ARGS = run --rm \
	--pid=host --cgroupns=host --network=host --privileged \
	-v /etc/os-release:/etc/os-release-host:ro \
	-v /boot/config-$(UNAME_R):/boot/config-$(UNAME_R):ro \
	-v $(PWD):/tracee \
	-v /sys/kernel/security:/sys/kernel/security:ro \
	-e LIBBPFGO_OSRELEASE_FILE=/etc/os-release-host \
	$(DOCKER_RUN_ENV) \
	-v /tmp/tracee:/tmp/tracee:rw

#
# tracee-make's shell
#

.PHONY: alpine-shell
alpine-shell: \
	| .check_$(CMD_DOCKER) \
	.check_tree
#
	$(CMD_DOCKER) \
		$(DOCKER_RUN_ARGS) \
		-it $(ALPINE_MAKE_CONTNAME) \
		/bin/bash

.PHONY: ubuntu-shell
ubuntu-shell: \
	| .check_$(CMD_DOCKER) \
	.check_tree
#
	$(CMD_DOCKER) \
		$(DOCKER_RUN_ARGS) \
		-it $(UBUNTU_MAKE_CONTNAME) \
		/bin/bash

#
# tracee-make replacing make
#

.PHONY: alpine-make
alpine-make: \
	| .check_$(CMD_DOCKER) \
	.check_tree
#
ifneq ($(ARG),)
	$(CMD_DOCKER) \
		$(DOCKER_RUN_ARGS) \
		$(ALPINE_MAKE_CONTNAME) \
		$(MAKE) $(ARG)
endif

.PHONY: ubuntu-make
ubuntu-make: \
	| .check_$(CMD_DOCKER) \
	.check_tree
#
ifneq ($(ARG),)
	$(CMD_DOCKER) \
		$(DOCKER_RUN_ARGS) \
		$(UBUNTU_MAKE_CONTNAME) \
		$(MAKE) $(ARG)
endif

#
# clean
#

.PHONY: clean
clean:
	$(MAKE) clean

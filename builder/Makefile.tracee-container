#
# Creates the official tracee containers images.
#

.PHONY: all
all: help

#
# make
#

.ONESHELL:
SHELL = /bin/sh

MAKEFLAGS += --no-print-directory

#
# environment
#

UNAME_M := $(shell uname -m)
UNAME_R := $(shell uname -r)

ifeq ($(UNAME_M),x86_64)
	ARCH = x86_64
	ALTARCH = amd64
endif

ifeq ($(UNAME_M),aarch64)
	ARCH = aarch64
	ALTARCH = arm64
endif

ifeq ($(ALTARCH),)
	@echo "can't find architecture"
	exit 1
endif

#
# tools
#

CMD_DOCKER ?= docker

.check_%:
#
	@command -v $* >/dev/null
	if [ $$? -ne 0 ]; then
		echo "missing required tool $*"
		exit 1
	else
		touch $@ # avoid target rebuilds due to inexistent file
	fi

#
# usage
#

.PHONY: help
help:
	@echo ""
	@echo "CREATES THE OFFICIAL TRACEE CONTAINER IMAGE"
	@echo ""
	@echo "To GENERATE tracee container:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-container build-tracee"
	@echo ""
	@echo "To EXECUTE tracee container:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-container run-tracee"
	@echo ""
	@echo "NOTES:"
	@echo ""
	@echo "  1. You may provide \"run\" arguments using the ARG variable. Example:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-container build-tracee"
	@echo ""
	@echo "    $$ make -f builder/Makefile.tracee-container run-tracee ARG=\"--help\""
	@echo ""
	@echo "    > This will run tracee using provided arguments."
	@echo ""

#
# requirements
#

.PHONY: .check_tree
.check_tree:
#
	@if [ ! -d ./builder ]; then
		echo "you must be in the root directory"
		exit 1
	fi

#
# create tracee
#

# BTFHUB is not set by default, but image should be built with BTFHUB=1.
ifeq ($(BTFHUB),)
BTFHUB=0
endif

ifeq ($(STATIC),)
STATIC=0
endif

SNAPSHOT ?= 0
TAG ?= latest

ifeq ($(SNAPSHOT),1)
	TAG=dev
endif

TRACEE_CONT_NAME = tracee:$(TAG)
TRACEE_CONT_DOCKERFILE = builder/Dockerfile.alpine-tracee-container

.PHONY: build-tracee
build-tracee: \
	| .check_$(CMD_DOCKER) \
	.check_tree
#
	$(CMD_DOCKER) build \
		--network host \
		-f $(TRACEE_CONT_DOCKERFILE) \
		-t $(TRACEE_CONT_NAME) \
		--build-arg=BTFHUB=$(BTFHUB) \
		--build-arg=STATIC=$(STATIC) \
		--build-arg=RELEASE_VERSION=$(RELEASE_VERSION) \
		--build-arg=FLAVOR=tracee-core \
		--target tracee-core \
		.

#
# run tracee
#

DOCKER_RUN_ARGS = run --rm \
	--pid=host --cgroupns=host --network host --privileged \
	-v /etc/os-release:/etc/os-release-host:ro \
	-v /boot/config-$(UNAME_R):/boot/config-$(UNAME_R):ro \
	-v /sys/kernel/security:/sys/kernel/security:ro \
	-e LIBBPFGO_OSRELEASE_FILE=/etc/os-release-host \
	-v /tmp/tracee:/tmp/tracee:rw

.PHONY: run-tracee
run-tracee: \
	| .check_$(CMD_DOCKER) \
	.check_tree
#
	$(CMD_DOCKER) \
		$(DOCKER_RUN_ARGS) \
		--rm -it $(TRACEE_CONT_NAME) \
		$(ARG)

#
# clean
#

.PHONY: clean
clean:
	$(MAKE) clean

# Creates a local docker building environment (alpine)

#
# alpine-base: install necessary packages and tools
#

FROM alpine:3.19 AS alpine-base
LABEL AS=alpine-base
USER root

# install needed environment
RUN apk --no-cache update && \
    apk --no-cache add sudo coreutils findutils bash git curl rsync && \
    apk --no-cache add clang14 llvm14 musl-dev libc6-compat make gcc linux-headers elfutils-dev libelf-static zlib-static zstd-static

# set up symlinks for Clang and LLVM tools
RUN rm -f /usr/bin/cc /usr/bin/clang /usr/bin/clang++ /usr/bin/llc /usr/bin/lld /usr/bin/clangd \
          /usr/bin/clang-format /usr/bin/llvm-strip /usr/bin/llvm-config /usr/bin/ld.lld /usr/bin/llvm-ar \
          /usr/bin/llvm-nm /usr/bin/llvm-objcopy /usr/bin/llvm-objdump /usr/bin/llvm-readelf /usr/bin/opt && \
    ln -s /usr/lib/llvm14/bin/clang /usr/bin/cc && \
    ln -s /usr/lib/llvm14/bin/clang /usr/bin/clang && \
    ln -s /usr/lib/llvm14/bin/clang++ /usr/bin/clang++ && \
    ln -s /usr/lib/llvm14/bin/clangd /usr/bin/clangd && \
    ln -s /usr/lib/llvm14/bin/clang-format /usr/bin/clang-format && \
    ln -s /usr/lib/llvm14/bin/lld /usr/bin/lld && \
    ln -s /usr/lib/llvm14/bin/llc /usr/bin/llc && \
    ln -s /usr/lib/llvm14/bin/llvm-strip /usr/bin/llvm-strip && \
    ln -s /usr/lib/llvm14/bin/llvm-config /usr/bin/llvm-config && \
    ln -s /usr/lib/llvm14/bin/ld.lld /usr/bin/ld.lld && \
    ln -s /usr/lib/llvm14/bin/llvm-ar /usr/bin/llvm-ar && \
    ln -s /usr/lib/llvm14/bin/llvm-nm /usr/bin/llvm-nm && \
    ln -s /usr/lib/llvm14/bin/llvm-objcopy /usr/bin/llvm-objcopy && \
    ln -s /usr/lib/llvm14/bin/llvm-objdump /usr/bin/llvm-objdump && \
    ln -s /usr/lib/llvm14/bin/llvm-readelf /usr/bin/llvm-readelf && \
    ln -s /usr/lib/llvm14/bin/opt /usr/bin/opt

# install bpftool from btfhub
RUN cd /tmp && \
    git clone https://github.com/aquasecurity/btfhub.git && \
    cd ./btfhub && \
    ./3rdparty/bpftool.sh

# install extra tools for testing things
RUN apk --no-cache add man-pages man-pages-posix bash-completion vim iproute2 vlan bridge-utils net-tools \
    netcat-openbsd iputils wget lynx w3m stress-ng

#
# go-setup: install Go and Go tools
#

FROM alpine-base AS go-setup
LABEL AS=go-setup
USER root

ARG GO_VERSION=1.24.0
ENV GOPATH=/go
ENV GOROOT=/usr/local/go
ENV GOTOOLCHAIN="auto"
ENV PATH=${GOPATH}/bin:${GOROOT}/bin:$PATH

# install Go
RUN TARGETARCH=$(uname -m | sed 's:x86_64:amd64:g' | sed 's:aarch64:arm64:g') && \
    curl -L -o go${GO_VERSION}.linux-${TARGETARCH}.tar.gz https://go.dev/dl/go${GO_VERSION}.linux-${TARGETARCH}.tar.gz && \
    tar -C /usr/local -xzf go${GO_VERSION}.linux-${TARGETARCH}.tar.gz && \
    rm go${GO_VERSION}.linux-${TARGETARCH}.tar.gz

# install Go tools
RUN go install honnef.co/go/tools/cmd/staticcheck@2025.1 && \
    go install github.com/incu6us/goimports-reviser/v3@v3.8.2 && \
    go install github.com/mgechev/revive@v1.7.0 && \
    go install github.com/kisielk/errcheck@v1.9.0

#
# user-setup: configure user environment and permissions
#

FROM go-setup AS user-setup
LABEL AS=user-setup
USER root

# allow TRACEE* and LIBBPFGO* environment variables through sudo
ARG uid=1000
ARG gid=1000

RUN echo "Defaults env_keep += \"LANG LC_* HOME EDITOR PAGER GIT_PAGER MAN_PAGER\"" > /etc/sudoers && \
    echo "Defaults env_keep += \"LIBBPFGO* TRACEE*\"" >> /etc/sudoers && \
    echo "root ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers && \
    echo "tracee ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers && \
    chmod 0440 /etc/sudoers

# prepare tracee user to be $UID:$GID host equivalent
RUN export uid=$uid gid=$gid && \
    mkdir -p /home/<USER>
    echo "tracee:x:${uid}:${gid}:Tracee,,,:/home/<USER>/bin/bash" >> /etc/passwd && \
    echo "tracee:x:${gid}:" >> /etc/group && \
    echo "tracee::99999:0:99999:7:::" >> /etc/shadow && \
    chown ${uid}:${gid} -R /home/<USER>
    echo "export PS1=\"\u@\h[\w]$ \"" > /home/<USER>/.bashrc && \
    echo "alias ls=\"ls --color\"" >> /home/<USER>/.bashrc && \
    echo "set -o vi" >> /home/<USER>/.bashrc && \
    ln -s /home/<USER>/.bashrc /home/<USER>/.profile

# adjust permissions
RUN chown -R tracee:tracee ${GOPATH}

#
# tracee-env: last stage for tracee building environment
#

FROM user-setup AS tracee-env
USER tracee
ENV HOME=/home/<USER>
WORKDIR /tracee

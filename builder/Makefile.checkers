.PHONY: all
all:
	@$(MAKE) help

#
# make
#

.ONESHELL:
SHELL = /bin/bash

MAKE = make -f $(shell find . -name Makefile.checkers)
GENERAL_MAKE = MAKEFLAGS="-j1 --no-print-directory" make
MAKEFLAGS += --no-print-directory

#
# tools
#

CMD_HEAD ?= head
CMD_CUT ?= cut
CMD_SED ?= sed
CMD_TR ?= tr
CMD_FIND ?= find
CMD_CLANG ?= clang
CMD_CLANG_FMT ?= clang-format-12 # mandatory for now, incompatible with 13 and newer
CMD_GOFMT ?= gofmt
CMD_GOIMPORTS ?= goimports-reviser
CMD_REVIVE ?= revive

.check_%:
#
	@command -v $* >/dev/null
	if [ $$? -ne 0 ]; then
		echo "missing required tool $*"
		exit 1
	else
		touch $@ # avoid target rebuilds due to inexistent file
	fi

#
# usage
#

.PHONY: help
help:
	@echo ""
	@echo "To check formatting you should run:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.checkers fmt-check"
	@echo ""
	@echo "To fix formatting you should run:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.checkers fmt-fix"
	@echo ""
	@echo "To check code you should run:"
	@echo ""
	@echo "    $$ make -f builder/Makefile.checkers code-check"
	@echo ""
	@echo "Note: you should run fmt-fix before doing a git commmit."
	@echo "Note: clang-format-12 is needed for eBPF code checks/fixes."
	@echo ""

#
# requirements
#

.PHONY: .check_tree
.check_tree:
#
	@if [ ! -d ./builder ]; then
		echo "you must be in the root directory"
		exit 1
	fi

#
# check formatting (clang-format, goimports-reviser, gofmt)
#

C_FILES_TO_BE_CHECKED = $(shell find ./pkg/ebpf/c/ -regextype posix-extended -regex '.*\.(h|c)' | xargs)

CMD_GOIMPORTS_COMPANY_PREFIXES ?= "github.com/aquasecurity"
CMD_GOIMPORTS_PROJECT ?= "github.com/aquasecurity/tracee"
CMD_GOIMPORTS_OUTPUT_FILE ?= "/tmp/check-goimports-fmt"

.PHONY: fmt-check
fmt-check: | \
	.check_tree \
	.check_$(CMD_CLANG_FMT) \
	.check_$(CMD_FIND) \
	.check_$(CMD_GOIMPORTS) \
	.check_$(CMD_GOFMT)
#
	@errors=0
	echo "Checking C and eBPF files and headers formatting..."
	$(CMD_CLANG_FMT) --dry-run $(C_FILES_TO_BE_CHECKED) > /tmp/check-c-fmt 2>&1
	clangfmtamount=$$(cat /tmp/check-c-fmt | wc -l)
	if [[ $$clangfmtamount -ne 0 ]]; then
		cat /tmp/check-c-fmt
		errors=1
	fi
	rm -f /tmp/check-c-fmt
#
	echo "Checking golang files imports formatting..."
	rm -f $(CMD_GOIMPORTS_OUTPUT_FILE)
	$(CMD_GOIMPORTS) \
		-output stdout \
		-list-diff \
		-company-prefixes $(CMD_GOIMPORTS_COMPANY_PREFIXES) \
		-project-name $(CMD_GOIMPORTS_PROJECT) \
		./... 2>/dev/null > $(CMD_GOIMPORTS_OUTPUT_FILE)
	goimportsamount=$$(grep -cve '^\s*$$' $(CMD_GOIMPORTS_OUTPUT_FILE))
	if [[ $$goimportsamount -ne 0 ]]; then
		errors=1
	fi
	if [[ $$errors -ne 0 ]]; then
		cat $(CMD_GOIMPORTS_OUTPUT_FILE)
		echo
		echo "Please fix formatting errors in the files above!"
		echo "Use: $(MAKE) fmt-fix target".
		echo
		exit 1
	fi
#
	echo "Checking golang files formatting..."
	$(CMD_GOFMT) -l -s -d . | tee /tmp/check-go-fmt
	gofmtamount=$$(cat /tmp/check-go-fmt | wc -l)
	if [[ $$gofmtamount -ne 0 ]]; then
		errors=1
	fi
	if [[ $$errors -ne 0 ]]; then
		echo
		echo "Please fix formatting errors above!"
		echo "Use: $(MAKE) fmt-fix target".
		echo
		exit 1
	fi
	rm -f /tmp/check-go-fmt

#
# golang linting (revive)
#

.PHONY: lint-check
lint-check: | \
	.check_tree \
	.check_$(CMD_REVIVE)
#
	@errors=0
	echo "Linting golang code..."
	$(CMD_REVIVE) -config .revive.toml ./...

#
# fix formatting (clang-format, goimports-reviser, gofmt)
#

.PHONY: fmt-fix
fmt-fix: | \
	.check_tree \
	.check_$(CMD_CLANG_FMT) \
	.check_$(CMD_GOIMPORTS) \
	.check_$(CMD_GOFMT)
#
	@echo "Fixing C and eBPF files and headers formatting..."
	$(CMD_CLANG_FMT) -i --verbose $(C_FILES_TO_BE_CHECKED)
#
	echo "Fixing golang files imports formatting..."
	$(CMD_GOIMPORTS) \
		-company-prefixes $(CMD_GOIMPORTS_COMPANY_PREFIXES) \
		-project-name $(CMD_GOIMPORTS_PROJECT) \
		./... 2>/dev/null
#
	echo "Fixing golang files formatting..."
	$(CMD_GOFMT) -l -s -d . > /tmp/patch.$$
	patch -p0 < /tmp/patch.$$
	rm /tmp/patch.$$

#
# check code (go vet, static checkers, errcheck)
#

.PHONY: code-check
code-check: | \
	.check_tree
#
	@set -e; \
	echo "Checking Golang vet..."; \
	$(GENERAL_MAKE) check-vet; \
	echo "Checking Golang with StaticChecker..."; \
	$(GENERAL_MAKE) -j1 check-staticcheck; \
	echo "Checking Golang with errcheck..."; \
	$(GENERAL_MAKE) -j1 check-err

#
# clean
#

.PHONY: clean
clean:
	rm -f .check_tree
	rm -f .check_$(CMD_CLANG_FMT)

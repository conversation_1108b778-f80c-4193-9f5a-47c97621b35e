#
# Creates a local docker building environment (ubuntu).
#

FROM ubuntu:jammy

ARG uid=1000
ARG gid=1000

#
# Version
#

ARG GO_VERSION=1.24.0

# install needed environment

RUN export DEBIAN_FRONTEND=noninteractive && \
    apt-get update && \
    apt-get install -y sudo coreutils findutils && \
    apt-get install -y bash git curl rsync && \
    apt-get install -y llvm clang clangd clang-format golang make gcc && \
    apt-get install -y llvm-12 clang-12 clangd-12 clang-format-12 && \
    apt-get install -y linux-headers-generic && \
    apt-get install -y libelf-dev && \
    apt-get install -y zlib1g-dev && \
    apt-get install -y libzstd-dev && \
    update-alternatives --install /usr/bin/clang clang /usr/bin/clang-12 130 --slave /usr/bin/clang++ clang++ /usr/bin/clang++-12 --slave /usr/bin/llc llc /usr/bin/llc-12 --slave /usr/bin/clang-format clang-format /usr/bin/clang-format-12 --slave /usr/bin/clangd clangd /usr/bin/clangd-12 && \
    update-alternatives --install /usr/bin/clang clang /usr/bin/clang-14 140 --slave /usr/bin/clang++ clang++ /usr/bin/clang++-14 --slave /usr/bin/llc llc /usr/bin/llc-14 --slave /usr/bin/clang-format clang-format /usr/bin/clang-format-14 --slave /usr/bin/clangd clangd /usr/bin/clangd-14

# install bpftool from btfhub

RUN cd /tmp && \
    git clone https://github.com/aquasecurity/btfhub.git && \
    cd ./btfhub && \
    ./3rdparty/bpftool.sh

# extra tools for testing things

RUN export DEBIAN_FRONTEND=noninteractive && \
    apt-get install -y man bash-completion vim && \
    apt-get install -y iproute2 vlan bridge-utils net-tools && \
    apt-get install -y netcat-openbsd iputils-ping && \
    apt-get install -y wget lynx w3m && \
    apt-get install -y stress

# allow TRACEE* and LIBBPFGO* environment variables through sudo

RUN echo "Defaults env_keep += \"LANG LC_* HOME EDITOR PAGER GIT_PAGER MAN_PAGER\"" > /etc/sudoers && \
    echo "Defaults env_keep += \"LIBBPFGO* TRACEE*\"" >> /etc/sudoers && \
    echo "root ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers && \
    echo "tracee ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers && \
    chmod 0440 /etc/sudoers

# prepare tracee user to be $UID:$GID host equivalent

RUN export uid=$uid gid=$gid && \
    mkdir -p /home/<USER>
    echo "tracee:x:${uid}:${gid}:Tracee,,,:/home/<USER>/bin/bash" >> /etc/passwd && \
    echo "tracee:x:${gid}:" >> /etc/group && \
    echo "tracee::99999:0:99999:7:::" >> /etc/shadow && \
    chown ${uid}:${gid} -R /home/<USER>
    echo "export PS1=\"\u@\h[\w]$ \"" > /home/<USER>/.bashrc && \
    echo "alias ls=\"ls --color\"" >> /home/<USER>/.bashrc && \
    echo "set -o vi" >> /home/<USER>/.bashrc && \
    ln -s /home/<USER>/.bashrc /home/<USER>/.profile

# install extra packages (if needed)
RUN export DEBIAN_FRONTEND=noninteractive && \
    altarch=$(uname -m | sed 's:x86_64:amd64:g' | sed 's:aarch64:arm64:g') && \
    apt-get update && \
    curl -L -o /tmp/golang.tar.gz https://go.dev/dl/go${GO_VERSION}.linux-${altarch}.tar.gz && \
    tar -C /usr/local -xzf /tmp/golang.tar.gz && \
    update-alternatives --install /usr/bin/go go /usr/local/go/bin/go 1 && \
    update-alternatives --install /usr/bin/gofmt gofmt /usr/local/go/bin/gofmt 1

# install staticcheck

RUN GOROOT=/usr/local/go GOPATH=$HOME/go \
    go install honnef.co/go/tools/cmd/staticcheck@2025.1 && \
    cp $HOME/go/bin/staticcheck /usr/bin/

# install goimports-reviser

RUN GOROOT=/usr/local/go GOPATH=$HOME/go \
    go install github.com/incu6us/goimports-reviser/v3@v3.8.2 && \
    cp $HOME/go/bin/goimports-reviser /usr/bin/

# install revive

RUN GOROOT=/usr/local/go GOPATH=$HOME/go \
    go install github.com/mgechev/revive@v1.7.0 && \
    cp $HOME/go/bin/revive /usr/bin/

# install errcheck

RUN GOROOT=/usr/local/go GOPATH=$HOME/go \
    go install github.com/kisielk/errcheck@v1.9.0 && \
    cp $HOME/go/bin/errcheck /usr/bin/

USER tracee
ENV HOME=/home/<USER>
WORKDIR /tracee

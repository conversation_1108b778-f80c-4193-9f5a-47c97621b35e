#
# Creates the official tracee containers.
#

ARG BTFHUB=0
ARG FLAVOR=tracee-ebpf-core

#
# Version
#

ARG GO_VERSION=1.24.0


#
# tracee-base
#

FROM alpine:3.19 AS tracee-base
LABEL AS=tracee-base
USER root

# install base environment

RUN apk --no-cache update && \
    apk --no-cache add coreutils && \
    apk --no-cache add sudo curl && \
    apk --no-cache add libelf zlib zstd && \
    apk --no-cache add libc6-compat

#
# tracee-make-base
#

FROM tracee-base AS tracee-make-base
LABEL AS=tracee-make-base
USER root

# install needed environment

RUN apk --no-cache update && \
    apk --no-cache add bash git rsync && \
    apk --no-cache add coreutils findutils && \
    apk --no-cache add llvm14 clang14 && \
    apk --no-cache add make gcc && \
    apk --no-cache add musl-dev && \
    apk --no-cache add linux-headers && \
    apk --no-cache add elfutils-dev && \
    apk --no-cache add libelf-static && \
    apk --no-cache add zlib-static && \
    apk --no-cache add zstd-static && \
    apk --no-cache add binutils-gold && \
    rm -f /usr/bin/cc && \
    rm -f /usr/bin/clang && \
    rm -f /usr/bin/clang++ && \
    rm -f /usr/bin/llc && \
    rm -f /usr/bin/lld && \
    rm -f /usr/bin/clangd && \
    rm -f /usr/bin/clang-format && \
    rm -f /usr/bin/llvm-strip && \
    rm -f /usr/bin/llvm-config && \
    rm -f /usr/bin/ld.lld && \
    rm -f /usr/bin/llvm-ar && \
    rm -f /usr/bin/llvm-nm && \
    rm -f /usr/bin/llvm-objcopy && \
    rm -f /usr/bin/llvm-objdump && \
    rm -f /usr/bin/llvm-readelf && \
    rm -f /usr/bin/opt && \
    ln -s /usr/lib/llvm14/bin/clang /usr/bin/cc && \
    ln -s /usr/lib/llvm14/bin/clang /usr/bin/clang && \
    ln -s /usr/lib/llvm14/bin/clang++ /usr/bin/clang++ && \
    ln -s /usr/lib/llvm14/bin/clangd /usr/bin/clangd && \
    ln -s /usr/lib/llvm14/bin/clang-format /usr/bin/clang-format && \
    ln -s /usr/lib/llvm14/bin/lld /usr/bin/lld && \
    ln -s /usr/lib/llvm14/bin/llc /usr/bin/llc && \
    ln -s /usr/lib/llvm14/bin/llvm-strip /usr/bin/llvm-strip && \
    ln -s /usr/lib/llvm14/bin/llvm-config /usr/bin/llvm-config && \
    ln -s /usr/lib/llvm14/bin/ld.lld /usr/bin/ld.lld && \
    ln -s /usr/lib/llvm14/bin/llvm-ar /usr/bin/llvm-ar && \
    ln -s /usr/lib/llvm14/bin/llvm-nm /usr/bin/llvm-nm && \
    ln -s /usr/lib/llvm14/bin/llvm-objcopy /usr/bin/llvm-objcopy && \
    ln -s /usr/lib/llvm14/bin/llvm-objdump /usr/bin/llvm-objdump && \
    ln -s /usr/lib/llvm14/bin/llvm-readelf /usr/bin/llvm-readelf && \
    ln -s /usr/lib/llvm14/bin/opt /usr/bin/opt

# install GO
ARG GO_VERSION
RUN TARGETARCH=$(uname -m | sed 's:x86_64:amd64:g' | sed 's:aarch64:arm64:g') && \
    curl -L -o go${GO_VERSION}.linux-${TARGETARCH}.tar.gz https://go.dev/dl/go${GO_VERSION}.linux-${TARGETARCH}.tar.gz && \
    tar -C /usr/local -xzf go${GO_VERSION}.linux-${TARGETARCH}.tar.gz && \
    echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile && \
    echo 'export GOROOT=/usr/local/go' >> /etc/profile && \
    echo 'export GOPATH=$HOME/go' >> /etc/profile && \
    echo 'export GOTOOLCHAIN=auto' >> /etc/profile && \
    echo 'export PATH=$PATH:$GOPATH/bin' >> /etc/profile

# install bpftool from btfhub

RUN cd /tmp && \
    git clone https://github.com/aquasecurity/btfhub.git && \
    cd ./btfhub && \
    ./3rdparty/bpftool.sh

#
# tracee-make
#

FROM tracee-make-base AS tracee-make
LABEL AS=tracee-make
ARG BTFHUB
ARG STATIC
ARG RELEASE_VERSION
USER root
ENV HOME=/tracee
WORKDIR /tracee

COPY . /tracee

RUN source /etc/profile && \
    make clean && \
    BTFHUB=$BTFHUB STATIC=$STATIC RELEASE_VERSION=$RELEASE_VERSION make tracee tracee-ebpf && \
    make tracee-rules && \
    make tracee-operator && \
    make signatures && \
    rm -rf ./3rdparty/btfhub/ && \
    rm -rf ./3rdparty/btfhub-archive/

#
# tracee-core (tracee-base as base)
#

FROM tracee-base AS tracee-core
LABEL AS=tracee-core
USER root
ENV HOME=/tracee
WORKDIR /tracee

RUN apk --no-cache add mandoc

COPY --from=tracee-make /tracee/dist/tracee /tracee
COPY --from=tracee-make /tracee/dist/tracee-ebpf /tracee
COPY --from=tracee-make /tracee/dist/tracee-rules /tracee
COPY --from=tracee-make /tracee/dist/tracee-operator /tracee
COPY --from=tracee-make /tracee/dist/signatures/ /tracee/signatures/
COPY --from=tracee-make /tracee/docs/man/ /tracee/docs/man/
COPY --from=tracee-make /tracee/builder/entrypoint.sh /tracee/entrypoint.sh
COPY --from=tracee-make /tracee/cmd/tracee-rules/templates/ /tracee/templates/

ENTRYPOINT ["/tracee/entrypoint.sh"]

#
# tracee
#

FROM $FLAVOR
USER root
ENV HOME=/tracee
WORKDIR /tracee

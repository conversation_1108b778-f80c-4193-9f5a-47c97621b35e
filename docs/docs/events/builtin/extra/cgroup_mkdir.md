
# cgroup_mkdir

## Intro

**cgroup_mkdir** - An event that gets triggered every time a new cgroup
*directory is created.

## Description

The `cgroup_mkdir` event is designed to track the creation of new directories
within the cgroup filesystem. Given that containers utilize control groups
(`cgroup`) for resource management, the creation of a new directory can often
signal the initiation or termination of a container.

Thus, by monitoring these directory creation events through `cgroup_mkdir`, one
can gain valuable insights into container operations, resource allocations, and
overall container activity within the system.

This event serves as a valuable tool for both observing container lifecycle
events and understanding the broader container orchestration dynamics.

## Arguments

- **cgroup_id** (`u64`): The unique identifier for the cgroup.
- **cgroup_path** (`const char*`): The file system path to the cgroup directory.
- **hierarchy_id** (`u32`): An identifier that indicates the hierarchy level of the cgroup.

## Hooks

### tracepoint__cgroup__cgroup_mkdir

#### Type

Raw tracepoint (using `raw_tracepoint/cgroup_mkdir`).

#### Purpose

To observe and capture details every time a new `cgroup` directory is created.
Information about the cgroup's unique identifier, its file system path, and its
hierarchy level is captured.

## Example Use Case

1. Monitoring container activities: By observing cgroup directory creations, a
system can detect when new containers are spun up, potentially identifying
unexpected or malicious activity.
2. Resource accounting: Track the creation of new cgroups to better understand
resource utilization on a per-container basis.

## Related Events

- container_create: This derived event provides detailed information about
the container that corresponds to the created cgroup directory.

> Note: This document was generated by OpenAI with a human review process.

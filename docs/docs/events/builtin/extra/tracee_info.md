# tracee_info

## Intro

tracee_info - An event that exports some relevant data of <PERSON><PERSON> upon startup.

## Description

This event, created in user-mode during <PERSON><PERSON>'s initialization, is typically the first event emitted. It provides valuable metadata about <PERSON><PERSON>'s configuration and runtime environment, which can be helpful for event processing and troubleshooting.

The event was created also with <PERSON><PERSON>'s File Source in mind, to provide information about how <PERSON><PERSON> ran during the original capture.

## Arguments

* `boot_time`:`u64`[U] - the boot time of the system that <PERSON><PERSON> is running on, relative to the Unix epoch.
* `start_time`:`u64`[U] - the time the <PERSON><PERSON> process started relative to system boot time.
* `version`:`const char*`[U] - Tracee version.

## Hooks

## Example Use Case

The event could be used to calculate the relative time of events since <PERSON><PERSON>'s start.

## Related Events

`init_namespaces`
## ICMP

The Internet Control Message Protocol (ICMP) is a network layer protocol within
the Internet protocol suite (TCP/IP). ICMP is primarily used for diagnostic and
control purposes, allowing devices in a network to communicate error and status
information about network conditions. It is commonly employed by networking
utilities and diagnostic tools such as ping and traceroute. The ICMP protocol
operates at the network layer, just like IP, and it uses specific message types
encapsulated within ICMP packets.

The ICMP header consists of several fields:

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|     Type (8 bits)             |    Code (8 bits)              |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                 Checksum (16 bits)                            |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                       Rest of Header (32 bits)                |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                         Data (variable length)                +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

1. **Type (8 bits):** The Type field identifies the purpose or type of ICMP message being sent. Common types include Echo Request (ping), Destination Unreachable, Time Exceeded, and Redirect, among others.
2. **Code (8 bits):** The Code field provides additional context or details related to the specific ICMP message type. It works in conjunction with the Type field to refine the message's meaning.
3. **Checksum (16 bits):** The Checksum field is used to ensure the integrity of the ICMP message and its header during transmission.
4. **Rest of Header (32 bits):** Depending on the specific ICMP message type, this field may contain additional information, such as an Identifier and Sequence Number for Echo Request and Echo Reply messages.
5. **Data (variable length):** The Data portion of the ICMP packet contains the payload specific to the ICMP message type. For example, an Echo Request message contains data that will be echoed back in the corresponding Echo Reply.

> Because ICMP messages can potentially reveal information about a network's structure, they may be filtered or controlled by security mechanisms in certain network environments to mitigate potential security risks.

ICMP (Internet Control Message Protocol) includes several message types, but
some are more commonly used than others for network diagnostics and management.
Here are descriptions of some of the most commonly used ICMP message types along
with their headers:

1. **Echo Request and Echo Reply (Ping):**
    - **Type**: 8 (Echo Request), 0 (Echo Reply)
    - **Description**: The Echo Request and Echo Reply messages, often referred to as "ping," are used to test network connectivity and measure round-trip time. An Echo Request is sent to a destination, and the destination responds with an Echo Reply if it's reachable. The ICMP header for both types includes a 32-bit Identifier and a 32-bit Sequence Number to identify the request and response pairs.

2. **Destination Unreachable:**
    - **Type**: 3
    - **Description**: Destination Unreachable messages are generated by routers or gateways to inform the sender that a destination host or network is unreachable. The Code field specifies the reason for the unreachability, such as "Host Unreachable" or "Port Unreachable." The ICMP header includes a portion of the original packet's IP header and data payload.

3. **Time Exceeded:**
    - **Type**: 11
    - **Description**: Time Exceeded messages are sent by routers when they discard a packet due to its Time to Live (TTL) reaching zero. This indicates a network loop or routing issue. The ICMP header includes the original packet's IP header and the first 8 bytes of its data payload.

4. **Redirect Message:**
    - **Type**: 5
    - **Description**: Redirect messages are used by routers to inform the sender that there's a better next-hop gateway for a particular destination. The Code field specifies whether the redirection is for the host or the network. The ICMP header includes the original packet's IP header.

5. **Parameter Problem:**
    - **Type**: 12
    - **Description**: Parameter Problem messages are generated when a router encounters an issue with the IP header of a received packet. The ICMP header includes a Pointer field that indicates the location of the error within the IP header and 8 bytes of the original IP header.

These are some of the most commonly used ICMP message types. Each type serves a specific purpose in network diagnostics, troubleshooting, and management. ICMP provides essential feedback for network administrators to identify and resolve issues, improve network performance, and ensure reliable communication. The headers of these ICMP messages include critical information for diagnosing network problems and understanding the context of the messages.

### net_packet_icmp

The `net_packet_icmp` event provides one event for each existing ICMP packet
that reaches or leaves one of the processes being traced (or even "all OS
processes for the default run"). As arguments for this event you will find:
`src`, `dst`, `metadata` arguments (common to all networking events) and all `ICMP header
fields`.

Example:

```console
$ tracee --output json --events net_packet_icmp
```

```json
{"timestamp":1696270181814910395,"threadStartTime":1696270180811296936,"processorId":6,"processId":1094692,"cgroupId":5650,"threadId":1094692,"parentProcessId":1037836,"hostProcessId":1094692,"hostThreadId":1094692,"hostParentProcessId":1037836,"userId":1000,"mountNamespace":4026531841,"pidNamespace":4026531836,"processName":"ping","executable":{"path":""},"hostName":"rugged","containerId":"","container":{},"kubernetes":{},"eventId":"2004","eventName":"net_packet_icmp","matchedPolicies":[""],"argsNum":3,"returnValue":0,"syscall":"sendto","stackAddresses":[0],"contextFlags":{"containerStarted":false,"isCompat":false},"threadEntityId":4158935444,"processEntityId":4158935444,"parentEntityId":2142180145,"args":[{"name":"src","type":"const char*","value":"**************"},{"name":"dst","type":"const char*","value":"*******"},{"name":"proto_icmp","type":"trace.ProtoICMP","value":{"typeCode":"EchoRequest","checksum":38217,"id":6,"seq":2}}]}
{"timestamp":1696270181823805929,"threadStartTime":1696270180811296936,"processorId":6,"processId":1094692,"cgroupId":5650,"threadId":1094692,"parentProcessId":1037836,"hostProcessId":1094692,"hostThreadId":1094692,"hostParentProcessId":1037836,"userId":1000,"mountNamespace":4026531841,"pidNamespace":4026531836,"processName":"ping","executable":{"path":""},"hostName":"rugged","containerId":"","container":{},"kubernetes":{},"eventId":"2004","eventName":"net_packet_icmp","matchedPolicies":[""],"argsNum":3,"returnValue":0,"syscall":"","stackAddresses":[0],"contextFlags":{"containerStarted":false,"isCompat":false},"threadEntityId":4158935444,"processEntityId":4158935444,"parentEntityId":2142180145,"args":[{"name":"src","type":"const char*","value":"*******"},{"name":"dst","type":"const char*","value":"**************"},{"name":"proto_icmp","type":"trace.ProtoICMP","value":{"typeCode":"EchoReply","checksum":40265,"id":6,"seq":2}}]}
{"timestamp":1696270182815992107,"threadStartTime":1696270180811296936,"processorId":1,"processId":1094692,"cgroupId":5650,"threadId":1094692,"parentProcessId":1037836,"hostProcessId":1094692,"hostThreadId":1094692,"hostParentProcessId":1037836,"userId":1000,"mountNamespace":4026531841,"pidNamespace":4026531836,"processName":"ping","executable":{"path":""},"hostName":"rugged","containerId":"","container":{},"kubernetes":{},"eventId":"2004","eventName":"net_packet_icmp","matchedPolicies":[""],"argsNum":3,"returnValue":0,"syscall":"sendto","stackAddresses":[0],"contextFlags":{"containerStarted":false,"isCompat":false},"threadEntityId":4158935444,"processEntityId":4158935444,"parentEntityId":2142180145,"args":[{"name":"src","type":"const char*","value":"**************"},{"name":"dst","type":"const char*","value":"*******"},{"name":"proto_icmp","type":"trace.ProtoICMP","value":{"typeCode":"EchoRequest","checksum":17732,"id":6,"seq":3}}]}
{"timestamp":1696270182825223127,"threadStartTime":1696270180811296936,"processorId":6,"processId":1094692,"cgroupId":5650,"threadId":1094692,"parentProcessId":1037836,"hostProcessId":1094692,"hostThreadId":1094692,"hostParentProcessId":1037836,"userId":1000,"mountNamespace":4026531841,"pidNamespace":4026531836,"processName":"ping","executable":{"path":""},"hostName":"rugged","containerId":"","container":{},"kubernetes":{},"eventId":"2004","eventName":"net_packet_icmp","matchedPolicies":[""],"argsNum":3,"returnValue":0,"syscall":"","stackAddresses":[0],"contextFlags":{"containerStarted":false,"isCompat":false},"threadEntityId":4158935444,"processEntityId":4158935444,"parentEntityId":2142180145,"args":[{"name":"src","type":"const char*","value":"*******"},{"name":"dst","type":"const char*","value":"**************"},{"name":"proto_icmp","type":"trace.ProtoICMP","value":{"typeCode":"EchoReply","checksum":19780,"id":6,"seq":3}}]}
{"timestamp":1696270183817423936,"threadStartTime":1696270180811296936,"processorId":6,"processId":1094692,"cgroupId":5650,"threadId":1094692,"parentProcessId":1037836,"hostProcessId":1094692,"hostThreadId":1094692,"hostParentProcessId":1037836,"userId":1000,"mountNamespace":4026531841,"pidNamespace":4026531836,"processName":"ping","executable":{"path":""},"hostName":"rugged","containerId":"","container":{},"kubernetes":{},"eventId":"2004","eventName":"net_packet_icmp","matchedPolicies":[""],"argsNum":3,"returnValue":0,"syscall":"sendto","stackAddresses":[0],"contextFlags":{"containerStarted":false,"isCompat":false},"threadEntityId":4158935444,"processEntityId":4158935444,"parentEntityId":2142180145,"args":[{"name":"src","type":"const char*","value":"**************"},{"name":"dst","type":"const char*","value":"*******"},{"name":"proto_icmp","type":"trace.ProtoICMP","value":{"typeCode":"EchoRequest","checksum":62525,"id":6,"seq":4}}]}
{"timestamp":1696270183826709353,"threadStartTime":1696270180811296936,"processorId":6,"processId":1094692,"cgroupId":5650,"threadId":1094692,"parentProcessId":1037836,"hostProcessId":1094692,"hostThreadId":1094692,"hostParentProcessId":1037836,"userId":1000,"mountNamespace":4026531841,"pidNamespace":4026531836,"processName":"ping","executable":{"path":""},"hostName":"rugged","containerId":"","container":{},"kubernetes":{},"eventId":"2004","eventName":"net_packet_icmp","matchedPolicies":[""],"argsNum":3,"returnValue":0,"syscall":"","stackAddresses":[0],"contextFlags":{"containerStarted":false,"isCompat":false},"threadEntityId":4158935444,"processEntityId":4158935444,"parentEntityId":2142180145,"args":[{"name":"src","type":"const char*","value":"*******"},{"name":"dst","type":"const char*","value":"**************"},{"name":"proto_icmp","type":"trace.ProtoICMP","value":{"typeCode":"EchoReply","checksum":64573,"id":6,"seq":4}}]}
```

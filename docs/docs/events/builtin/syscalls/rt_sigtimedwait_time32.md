
# rt_sigtimedwait_time32

## Intro
rt_sigtimedwait_time32 - wait synchronously for queued signals arising within a given time

## Description
rt_sigtimedwait_time32 is a system call that allows waiting for one of the pending signals occurring in the signal set `uthese` with a timeout of `uts`, which is rounded up to the system clock granularity (specified by `uts`). The signal's number and its associated `uinfo` are returned in the `uinfo` argument if available. `sigsetsize` is used to indicate size in bytes of the signal set pointed by `uthese`. Status of the signal for which the call was unblocked is returned. The time is limited by the `uts->tv_sec` and `uts->tv_nsec` arguments. If timeout is set to 0 and no signals are present on the queue, then this will cause an error. 

This system call provides an easy to use way to wait for signals that arrive over a short period of time with the benefit of minimizing the chances of missing them.

## Arguments
* `uthese`:`sigset_t*`<KU> - pointer to a signal set containing the signals which are to be waited for.
* `uinfo`:`siginfo_t*`[K] - pointer to a `siginfo_t` structure to store the information about the signal, may be `NULL`.
* `uts`:`struct old_timespec32*`[K] - a structure that specifies the end time for the timeout, may be `NULL`.
* `sigsetsize`:`size_t`[K] - number of bytes in the signal set pointed to by `uthese`.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value) 

## Hooks
### do_sigtimedwait
#### Type
Kprobe
#### Purpose
This function is hooked to measure the time it takes for each time `rt_sigtimedwait_time32` system call is executed.

### old_sigaction
#### Type
Kretprobe
#### Purpose
This function is hooked to record some information related to the signals and their structure when it is used in conjunctiob with the `rt_sigtimedwait_time32` system call.

## Example Use Case
The rt_sigtimedwait_time32 system call can be used to wait on synchronous signals arrivals with a given timeout, which is useful in workflows and systems which depend on real-time performance. 

## Issues
rt_sigtimedwait_time32 is susceptible to TOCTOU (time of check, time of use), because between the check and the actual use of the signal, the signal might change. 

## Related Events
* rt_sigtimedwait
* rt_sigprocmask

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

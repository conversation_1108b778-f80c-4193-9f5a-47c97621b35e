
# preadv

## Intro
preadv - read data from a file descriptor into multiple vectors.

## Description
preadv() allows the user to read data from a file descriptor into multiple
vectors. It acts like a combination of the read() and lseek() system calls:
it reads data from a given position in a file as specified by the pos_h and
pos_l arguments and stores it in the buffers which are provided.

The size of the data that is read is determined by the sum of the lengths of
all the individual iov buffers. If the sum is greater than SSIZE_MAX, then the
value stored in errno is set to EINVAL and -1 is returned. Additionally,
preadv() can read less than the amount requested if the data is not present
in the file.

Preadv() respects file positioning flags such as O_APPEND and O_DIRECT. These
flags are taken into consideration when determining the position from which
data is read.

Preadv() is atomic; when multiple threads/processes simultaneously try to
read the same file, they will be guaranteed to read the correct data.

## Arguments
* `fd`:int[K] - file descriptor referring to a file which is to be read.
* `iov`:const struct iovec*[K] - pointer to an array of struct iovec. Each element describes the destination buffer in which data will be stored. 
* `iovcnt`:unsigned long[K] - number of elements in iov array.
* `pos_l`:unsigned long[K] - least significant 32 bits of file offset from which the data will be read. 
* `pos_h`:unsigned long[K] - most significant 32 bits of file offset from which the data will be read.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### Syscall preadv:
#### Type
kprobe
#### Purpose
To monitor calls to preadv and collect data about these calls.

## Example Use Case
For example, preadv can be used to securely read metadata from a file on disk.

## Issues
Since preadv does not provide meaningful feedback when the data is not present
in the file, it may be difficult to detect errors in the process of
reading data.

## Related Events
pread,pread64,writev,writev64.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

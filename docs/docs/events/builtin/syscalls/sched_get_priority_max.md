
# `sched_get_priority_max`

## Intro
`sched_get_priority_max` - get the maximum priority value for a given scheduling policy

## Description
The `sched_get_prority_max` function returns the maximum priority value allowed by a given scheduling policy. It is used to obtain the highest allowed scheduling priority for the specified policy of a particular process.

`sched_get_priority_max` is most commonly used in combination with the `sched_get_priority_min` syscall, which returns the minimum priority value allowed by a given policy. Together, the two syscalls allow the range of valid scheduling priorities to be determined. It returns the maximum value allowed according to the operating systems configuration, but this value may be limited by the user or system.

## Arguments
* `policy`: `int`[K] - Specifies the scheduling policy for which the maximum priority value is to be returned. 

### Available Tags
* K - Originated from kernel-space

## Hooks
### `sched_get_priority_max`
#### Type
kprobes 
#### Purpose
To obtain the maximum priority value for a given scheduling policy.

## Example Use Case
This syscall can be used to get task or thread priority values in order to properly manage the scheduling of threads in a system.

## Issues
N/A

## Related Events
* `sched_get_priority_min` - get the minimum priority value for a given scheduling policy.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Trace<PERSON> recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

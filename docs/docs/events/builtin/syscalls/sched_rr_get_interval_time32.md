
# sched_rr_get_interval_time32

## Intro
sched_rr_get_interval_time32 - Retrieve the Round Robin scheduling interval of the thread with the corresponding pid.

## Description
The sched_rr_get_interval_time32 system call is used to determine the current Round Robin scheduling interval of the thread with the corresponding pid. It retrieves the interval on which the thread will be executed in Round Robin scheduling. 

There are several drawbacks to this syscall such as the fact that it only works with threads, not processes. In addition, the time precision of the interval is limited to second granularity. It also has some advantages such as the ability to determine the scheduling order of tasks.

## Arguments
* `pid`:`pid_t`[K] - Thread ID of the thread of whom the scheduling interval will be retrieved.
* `interval`:`struct old_timespec32*`[K] - Pointer to a structure of type old_timespec32 that will store the interval of the thread.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### kernel_sched_rr_get_interval_time32
#### Type
Tracepoint
#### Purpose
To understand the structure of Round Robin scheduling and the duration of tasks in this scheduling policy.

## Example Use Case
Monitoring and understanding the utilization of a CPU by a certain thread.

## Issues
This system call can only be used for threads, not processes.

## Related Events
* sched_setscheduler - To adjust scheduling policy and priority of processes
* sched_get_priority_min & sched_get_priority_max - To check the priority range of the current scheduling policy in the system.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

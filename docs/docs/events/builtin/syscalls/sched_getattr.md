
# sched_getattr

## Intro
sched_getattr - Retrieve scheduling policy and attributes from a given process.

## Description
The `sched_getattr()` system call retrieves the scheduling policy and attributes of a given process. It allows for retrieving of scheduling policy and attributes of other processes, if given the appropriate privileges. It supports the following flags: `SCHED_FLAG_KEEP_POLICY`, `SCHED_FLAG_KEEP_PARAMS`, and `SCHED_FLAG_ALLOW_NULL`. The `attr` argument is a pointer to a `struct sched_attr`, which stores the scheduling parameters.

The `sched_getattr()` system call retrieves the scheduling attributes of the given process. When used with the `SCHED_FLAG_KEEP_POLICY` and `SCHED_FLAG_KEEP_PARAMS` flags, this allows for retrieving of scheduling policy and attributes of other processes, if given the appropriate privileges.

## Arguments
- `pid`:`pid_t`[K] - The pid of the process to query.
- `attr`:`struct sched_attr*`[K] - A pointer to a `struct sched_attr` where the retrieved attributes will be stored.
- `size`:`unsigned int`[K] - The size of the `struct sched_attr` in bytes.
- `flags`:`unsigned int`[K] - Flags specifying how the scheduling attributes should be retrieved, allowed values are `SCHED_FLAG_KEEP_POLICY`, `SCHED_FLAG_KEEP_PARAMS`, and `SCHED_FLAG_ALLOW_NULL`.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### `sys_sched_getattr`
#### Type
Kprobe
#### Purpose
To monitor retrieval of scheduling attributes of a given process.

## Example Use Case
An administrator who needs to monitor scheduling policies of other processes.

## Issues
This system call requires appropriate privileges in order to monitor processes of other users.

## Related Events
- `sched_setattr` - Set scheduling policy and attributes for a given process. 
- `sched_setscheduler` - Set the scheduling policy for a given process.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

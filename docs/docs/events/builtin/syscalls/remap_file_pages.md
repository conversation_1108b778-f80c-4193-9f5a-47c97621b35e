
# remap_file_pages

## Intro
remap_file_pages - Map or remap part of a file into the process's address space

## Description
The `remap_file_pages` system call changes the protection and mapping of memory pages in the virtual address space of the calling process. It can remap an existing file mapping, create a new one, or unmap a range of pages previously mapped. It can also be used to change the permissions of the mapped pages. 

This call provides support for memory mapped files that can be used as shared memory or for other kinds of memory sharing among processes. 

An edge-case that can be encountered when using `remap_file_pages` is when the requested range of file pages is not available. This can be caused by the underlying file having been truncated, or if the requested mapping overlaps with another existing mapping.


## Arguments
* `addr`:`void*`[U] - Starting address of the mapping.
* `size`:`size_t`[U] - Size of the mapping.
* `prot`:`int`[U] - Memory protection flags to set for the mapping.
* `pgoff`:`size_t`[U] - File offset in bytes corresponding to the start of the mapping.
* `flags`:`int`[U] - Flags for the mapping. Can be set to 0 for default behavior. 

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_mmap_pgoff
#### Type
KProbes
#### Purpose
To monitor the `do_mmap_pgoff` syscall which is used by the kernel to handle `remap_file_pages` calls. 

### update_mmap_zero_pfn
#### Type
KProbes
#### Purpose
To monitor the `update_mmap_zero_pfn` syscall which is used by the kernel to handle `remap_file_pages` calls.

## Example Use Case
`remap_file_pages` can be used when a process needs to share parts of its address space with other processes. This can be done through the use of memory mapped files or shared memory.

## Issues
Due to its underlying implementation, `remap_file_pages` can be vulnerable to TOCTOU races. This means that between the time when the data is read and the data is used, other operations can take place which make the read data obsolete.

## Related Events
* `mmap`: used to create mappings in the virtual address space
* `mprotect`: used to change page permissions

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

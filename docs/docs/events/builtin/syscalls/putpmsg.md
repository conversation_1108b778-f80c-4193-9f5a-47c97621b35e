
# putpmsg

## Intro
putpmsg - send a message on a STREAMS pipe.

## Description
The putpmsg() system call is used to write a message on a STREAMS pipe. It enables a process to place zero to two messages on the pipe in an atomic manner, the combination of two messages making a single queued message.

## Arguments
* `fd`:`int` - the file descriptor associated with a STREAMS pipe.
* `ctlptr`:`struct strbuf`[K] - The argument points to a strbuf structure which is used to point to the control part of a message.  If a control part is not desired then a null pointer may be used.
* `dataptr`:`struct strbuf`[K] - The argument points to a strbuf structure which is used to point to the data part of a message, or may be a null pointer if no message data is requested.
* `flag`:`int` - The flag argument can take one of the values 0, MSG_BAND, MSG_HIPRI or MSG_ANY as defined below:

    * **MSG_BAND** — if the pipe grants priority bands, the specified priority band is requested.
    * **MSG_HIPRI** — the highest priority band is requested.
    * **MSG_ANY** — any priority band is acceptable.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### do_putpmsg
#### Type 
Kprobe
#### Purpose
To trace calls to putpmsg() system call.

## Example Use Case
One use case for putpmsg() is executing daemon services. By hooking the putpmsg() kprobe, one can check if and in what order daemon services are receiving messages each time. 

## Issues
As putpmsg() is specific to a STREAMS pipe, it will only be useful for applications that use it.

## Related Events
* link()
* open()
* read()
* write()

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

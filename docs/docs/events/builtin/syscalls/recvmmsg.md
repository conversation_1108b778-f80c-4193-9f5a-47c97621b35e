
# recvmmsg

## Intro
recvmmsg - Receive multiple messages on a socket

## Description
The recvmmsg() system call is used to receive multiple messages from a socket,
similar to recvmsg(), but allows passing a user-space array to retrieve multiple
messages into different buffers with a single call, instead of having to call the
function multiple times.

There are several advantages to using recvmmsg():
- It is much faster, because it can receive multiple messages at once, thus
  avoiding the need for multiple system calls.
- It can be used for better network performance, since it does fewer system
  calls, meaning less context switches from user-space to kernel-space.
- It can be used to receive multiple messages from different sockets (as
  opposed to recvmsg(), which receives from only one)

There are also a few drawbacks to using recvmmsg():
- It is not suitable for small messages, since the overhead for using it is
  higher than that of using the single message system call (recvmsg())
- If the socket is set to non-blocking, recvmmsg() will return an error if
  there are not enough messages in the queue, whereas recvmsg() would return
  immediately with the messages it has (if any).

## Arguments
* `sockfd`:`int`[K] - The file descriptor of the socket.
* `msgvec`:`struct mmsghdr*`[U] - An array of mmsghdr structures, each one
  containing a message that recvmmsg() receives into. The caller should ensure
  that this array is large enough for up to vlen messages.
* `vlen`:`unsigned int`[K] - The maximum size of the msgvec array.
* `flags`:`int`[K] - Socket flags, such as MSG_DONTWAIT.
* `timeout`:`struct timespec*`[K] - If not null, a pointer to a timespec
  structure to be used for timeouts.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_recvmmsg
#### Type
kprobe
#### Purpose
To monitor the receipt of multiple messages on a socket.

## Example Use Case
By using recvmmsg(), multiple file descriptors can be monitored in a single
system call, thus avoiding the need for making multiple system calls. This can
be useful when working with high-performance applications, such as distributed
systems, where a single system call needs to receive many messages at once.

## Issues
No known issues.

## Related Events
* recvmsg() - Receive a message from a socket.
* sendmmsg() - Send multiple messages on a socket.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

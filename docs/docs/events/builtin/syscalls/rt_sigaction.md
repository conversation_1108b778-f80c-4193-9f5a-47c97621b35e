
# rt_sigaction

## Intro
rt_sigaction - install or confirm action for specified signal.

## Description
`rt_sigaction` is a system call that changes the action taken by a process on receipt of a specific signal. Signal actions are explained in signal(7).

Using `rt_sigaction`, a process can specify the action to be taken when a specific signal is delivered. This can include ignoring the signal, catching it with a signal handler, or setting a flag in the process's signal mask. 

`rt_sigaction` is available on Linux systems and is a more recent, improved way of setting signal handlers compared to `signal` syscall. It can be used to install signal handlers that take an extra parameter, an `ucontext_t *`, which will be used by the handler to store the context of the signal where it was caught.

The main advantage of using this system call over the `signal` syscall is that it allows for the setting of a real-time signal, in addition to the usual POSIX signals. A critical limitation of the `signal` syscall is that it does not provide a way to set real-time signals, which are useful for applications needing to respond quickly to events.

`rt_sigaction` is also useful for working with signals that have been registered using `sigaction`. For example, `sigaction` can be used to register a signal handler, but `rt_sigaction` is the only way to retrieve the currently installed signal handler. 

## Arguments
* `signum`:int[K] - Signal to be handled, which can either be a POSIX signal or real-time signal.
* `act`:const struct sigaction*[K] - New signal action, or NULL to restore default action.
* `oldact`:struct sigaction*[K] - Output parameter which will return the previous signal action, or NULL if not required.
* `sigsetsize`:size_t[K] - Size of the sigset specified by `act` in bytes.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_signal
#### Type
Kprobes
#### Purpose
Hook function to capture signal handler changes made using `rt_sigaction` syscall. 

### signal_deliver_stop
#### Type
Kprobes
#### Purpose
Hook function to capture signals as they are delivered to the process. 

## Example Use Case 
`rt_sigaction` can be used to enable a process to react quickly to a signal sent from another process, or another thread in the same process. For example, a signal handler can be registered with `rt_sigaction` that sets a flag when the signal is received. The flag can then be checked in an application loop, and its presence will trigger an action. 

## Issues
It is possible to register a signal handler using `rt_sigaction` that does not take an additional parameter for the `ucontext_t *`, even though this parameter is required for real-time signals. While this will work for most OS versions without failing, this may cause unexpected results or failures on some systems. 

## Related Events
`signal`, `rt_sigprocmask`

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

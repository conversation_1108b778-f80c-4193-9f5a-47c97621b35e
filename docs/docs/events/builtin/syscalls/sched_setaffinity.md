
# sched_setaffinity

## Intro
sched_setaffinity - sets the CPU affinity mask of the process or thread with given pid.

## Description
The `sched_setaffinity()` system call is used to set the CPU affinity mask of the
process or thread with the specified `pid`.  The `cpusetsize` argument is the size
in bytes of the data pointed to by `mask`.  This allows applications to set the
processor affinity of threads to selected processors in a processor group.  This
eliminates the need to rely on system scheduler to selecting a processor for a
thread to execute on, and provides for more control for the user about the processor
a particular thread will run on.

The call will fail if the process does not have the appropriate privileges, or
if the process does not exist.  In addition, it can fail if there are more bits
in the `mask` than the system allows, or if the `mask` is invalid in some other
way. 

Due to the amount of detail that goes into implementing an optimized thread scheduling
strategy, there may be edge-cases or other considerations to take into account when
using this system call.

## Arguments
* `pid`: `pid_t`[K] - The pid of the process or thread whose affinity mask should be set.
* `cpusetsize`: `size_t`[K] - The size in bytes of the data pointed to by `mask`.
* `mask`: `unsigned long*`[K] - A pointer to an array of unsigned longs that comprises the CPU affinity mask.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_sched_setaffinity
#### Type
kprobe + uprobe
#### Purpose
To monitor when processes are setting the CPU affinity mask of their threads or child processes.

## Example Use Case
For applications that need to be distributed across CPU cores, scheduling affinity allows programs to manually dictate the cores a process should use, rather than relying on the operating system's scheduler. For example, if a group of threads need to prioritize one process over another, scheduling affinity can be used to ensure threads are allocated to the appropriate cores.

## Issues
The scheduling affinity is not always respected if the load is heavy on the system. This means that processes may land on cores that were not originally assigned to them due to the demand. In addition, it may not be possible to set the affinity to cores that do not exist on the system, and so the appropriate checks must be made before calling the system call.

## Related Events
sched_getaffinity - gets the CPU affinity mask of the process or thread with given pid.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

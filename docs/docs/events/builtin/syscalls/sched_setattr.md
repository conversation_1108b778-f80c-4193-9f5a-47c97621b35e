
# sched_setattr

## Intro
sched_setattr - sets the scheduling policy and attributes of a process 

## Description
sched_setattr takes a pid, attr and flags and performs the setting of a process's scheduling policy and attributes. The only allowable scheduling policy is the SCHED_DEADLINE policy. The function returns the scheduling policy that has been set for the process. This function is useful to set up specific parameters for a process that require real-time scheduling.

Edge cases that can occur when using this function include out of memory errors, when the system does not have enough memory to set the attributes specified, as well as policy errors, when attempting to set a policy that is not supported.

Another issue that could possibly occur is related to the scheduling priority set by the scheduling policy, namely when attempting to set up a higher priority than what is allowed based on the system permissions. 

## Arguments
* `pid`:`pid_t`[K] - the process id of the process whose scheduling policy and attributes are to be set.
* `attr`:`struct sched_attr*`[K] - the struct containing the scheduling policy, period and runtime and other scheduling details.
* `flags`:`unsigned int`[K] - flags that can be used to override the default behavior of sched_setattr.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### syscall_trace_enter
#### Type
Kprobe
#### Purpose
Tracing system calls for debugging, performance analysis, and other various use cases.

### sched_setattr
#### Type
Kprobe
#### Purpose
To trace calls to sched_setattr, in order to debug the scheduling of processes.

## Example Use Case
To verify the operation of application processes with real-time requirements.

## Issues
The flags argument of sched_setattr is currently not used.

## Related Events
sched_getattr, sched_setscheduler, sched_getscheduler

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

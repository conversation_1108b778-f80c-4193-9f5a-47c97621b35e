
# rmdir

## Intro
rmdir - delete an empty directory

## Description
The rmdir() system call deletes an empty directory. The directory must be
empty, and the calling process must have the necessary permissions to delete
it. It is possible to use relative pathnames to remove directories that are
not in the current working directory.

The rmdir() system call is often used to remove a parent directory that
contains a now-unneeded subdirectory. For example, if an application
creates a subdirectory "foo" under "/tmp", it should call rmdir() on "/tmp/foo"
as soon as the subdirectory is no longer needed.

## Arguments
* `pathname`:`const char`* - The pathname of the directory to delete.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### do_rmdir
#### Type
Kprobes
#### Purpose
To inspect when an empty directory is deleted.

## Example Use Case
Using rmdir in order to remove a directory created by an application after it is no longer needed.

## Issues
The rmdir system call will fail if the directory is not empty. Furthermore, it will not delete directories that contain files that have been unlinked, but are still open.

## Related Events
* unlink: to delete a file from a directory.
* rename: to rename or move a directory.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

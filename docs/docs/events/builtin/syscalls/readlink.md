
# readlink

## Intro
readlink - read the contents of a symbolic link

## Description
The `readlink()` system call reads the contents of a symbolic link and stores the 
result in the buffer pointed to by `buf`, which has a length of `bufsiz` bytes. 
This call does not follow symbolic links; it reads the contents of the link itself. 
The `readlink()` system call does not append a null byte to the buffer. 

## Arguments
* `pathname`:`const char*`[K] - Pointer to the symbolic link pathname.
* `buf`:`char*`[K+U] - A character array to store the contents of the link in.
* `bufsiz`:`size_t`[K] - Size of the character array.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### __x64_sys_readlink
#### Type
Kprobes
#### Purpose
To capture information regarding the use of the readlink system call.

## Example Use Case
A malicious user is attempting to use `readlink` for nefarious purposes. By 
hooking the `__x64_sys_readlink` function, an analyst can track and collect 
information regarding the use of the `readlink` syscall.

## Issues
None.

## Related Events
* open - The `open()` system call is used to open a file given its pathname. Used 
in conjunction with `readlink()`, as one cannot use `readlink()` with a relative 
path. 
* access - The `access()` system call is used to determine whether a process has 
permission to access a certain file or directory. Used in conjunction with 
`readlink()`, as an analyst may want to see if a user has access to certain file 
paths.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# mmap2

## Intro
mmap2 - establish a mapping to a memory region in the virtual address space of a process

## Description
The mmap2() system call creates a new mapping in the virtual address space of the calling process. The region is populated using the file descriptor passed in via the fd argument. These mappings may or may not be backed by physical memory or it could be mapped to the device I/O (found mostly in the kernel Space). This system call is mainly used to map a certain piece of memory to an I/O device or device buffers.

The addr argument allows the caller to specify a preferred starting address for the mapping, although this is not honoured in all cases. The length argument specifies the length of the mapping. The prot argument determines the protection of the mapping. It may contain any bitwise combination of the following flags:

* PROT_NONE: no access to the mapped region
* PROT_READ: reads from the mapped region allowed
* PROT_WRITE: writes to the mapped region allowed
* PROT_EXEC: execution from the mapped region allowed

The flags argument allows the caller to specify the type of mapping. It may contain any bitwise combination of:

* MAP_SHARED: The mapping is shared, thus changes in its contents reflect to all processes that have access to it.
* MAP_PRIVATE: The mapping is private to the calling process.
* MAP_HUGETLB: Allocate memory in units of huge pages.

The fd argument is used to specify the file descriptor to be used for the mapping. If it is set to -1, a anonymous mapping is created.

If the flags argument contains MAP_FIXED, the mapping will be placed at the address specified in the addr argument. Otherwise it is unspecified, although the kernel attempts to locate the mapping near the address given.

The pgoffset argument is used to specify a file-offset when mapping from a file.

## Arguments
* `addr`:`unsigned long`[U] - the address of the mapped memory.
* `length`:`unsigned long`[U] - the length of the mapped memory. 
* `prot`:`unsigned long`[U] - the protection of the memory. 
* `flags`:`unsigned long`[U] - flags that determine how the mapping is established. 
* `fd`:`unsigned long`[K] - file descriptor used to map the memory from. 
* `pgoffset`:`unsigned long`[K] - the file-offset used when mapping from a file.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_mmap2
#### Type
kprobe
#### Purpose
To monitor memory mapping requests from user space.

## Example Use Case
The mmap2 system call provides a way to map a certain piece of memory to an I/O device or device buffers. This could be useful when engineers need to access a device's memory without having to access memory directly in the kernel address space.

## Issues
mmap2 can fail with errors -EINVAL, -ENOMEM, and -EACCES if the given parameters do not meet the kernel requirements.

## Related Events
mmap, unmmap, msync

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

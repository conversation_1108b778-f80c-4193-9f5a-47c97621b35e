
# personality

## Intro
personality - change the personality of the calling process

## Description
The `personality` syscall is used to change the personality of the calling process. It can be used to provide the process with different versions of the Linux kernel API, allowing the process to run on different versions of Linux. The personality can also be used to change the behaviour of certain APIs, such as how signals are dispatched or whether threads are schedulable. It also determines which emulation mode the process uses. The personality specified by the argument is a bitmask, consisting of flags from `personality.h`.

There are some drawbacks to changing the personality of a process. It can result in the process being unable to run on certain versions of the kernel, and some features of the kernel may be unavailable in certain personality modes.

## Arguments
* `persona`: `unsigned long` - specifies the new personality for the calling process.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### sys_personality
#### Type
Kprobe
#### Purpose
To track when processes change their personality.

## Example Use Case
This syscall can be used to ensure that processes do not run in an incompatible kernel version by maintaining a list of kernel versions permissible for the process, and setting the process's personality accordingly.

## Issues
If the specified personality has not been compiled into the running kernel, the syscall will fail with the `ENOSYS` error code.

## Related Events
* `ptrace` - used for debugging and modifying processes
* `clone` - used for creating processes with different personality settings

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

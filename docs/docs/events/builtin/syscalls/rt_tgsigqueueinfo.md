
# rt_tgsigqueueinfo

## Intro
rt_tgsigqueueinfo - send signal information to a specific thread in a specific process.

## Description
The rt_tgsigqueueinfo() system call sends the signal sig with the additional information provided in info to any thread with the thread ID tid, who is part of the process with process ID tgid.

This syscall was developed to create a non-intrusive way to work with threads, not to risk them being suddenly paused or interrupted, as those events could cause lost progress.

## Arguments
* `tgid`:`pid_t` - the process ID (tgid) of the targeted process.
* `tid`:`pid_t` - the thread ID (tid) of the targeted thread within the process identified by tgid.
* `sig`:`int` - the signal to send.
* `info`:`siginfo_t*`[K U] - Data to be sent to the thread, defined in a _siginfo_t struct.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)

## Hooks
### do_rt_tgsigqueueinfo
#### Type
Kprobe.
#### Purpose
To trace the system calls and arguments of this event.

## Example Use Case
The rt_tgsigqueueinfo() system call can be used to send a signal to a specific thread within a specific process. This is often used for communication within multithreaded programs, or for debugging purposes.

## Issues
Due to the non-intrusive nature of this system call, there may be issues with latency as it does not force a thread to accept the signal if not ready for it.

## Related Events
* sigaction() - Used to set up a signal handler.
* waitid() - Used to wait for a thread to stop or signal to be delivered.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

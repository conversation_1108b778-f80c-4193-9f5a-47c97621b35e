
# pipe2

## Intro 
pipe2 - create a pipe and set its flags atomically

## Description
The pipe2() system call works similarly to pipe(), but it adds an extra argument flags, which contains the flags for opening the handle to the newly-created pipe. The flags argument is an OR-ed combination of the following ints: `O_CLOEXEC`, `O_NONBLOCK`, `O_DIRECT`. It is useful when the application is in a situation, where it might need to atomically create  a pipe, while setting some of its flags. 

## Arguments
* `pipefd`:`int[2]`[K] - Descriptor vector for the created pipe. The first element in the vector will refer to the read end of the pipe, while the second will refer to the write end. 
* `flags`:`int`[K] - flags to be used when opening the pipe. An OR-ed combination of `O_CLOEXEC`, `<PERSON>_NONBLOCK`, `O_DIRECT`

### Available Tags 
* K - Originated from kernel-space.

## Hooks
### pipe2
#### Type
Kprobes
#### Purpose
To trace the system call pipe2 and its arguments

## Example Use Case
pipe2 can be used in a situation in which a process needs to create a pipe atomically, and set some of its flags. For example, named pipes (FIFOs) which are used for Inter Process Communication (IPC), need to be opened with the `O_NONBLOCK` flag in order to prevent blocking when there is no process connected to the other side. 

## Issues
Zombie processes can appear if the write end of the pipe is not closed by any process.

## Related Events
* open

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# oldstat

## Intro
oldstat - get file status (struct __old_kernel_stat) for the given filename

## Description
The oldstat system call retrieves the file status (the content of the struct __old_kernel_stat) for the given filename. It fills the given structure with information about the file, like permissions, ownership, size, etc. It is typically used to gather data about a file, before attempting to use it in any way. It is important to note that its results might be subject to change due to the condition of the system (e.g. the user running the oldstat call does not have read access to the requested file).

## Arguments
* `filename`:`char*`[U] - Pointer to a buffer containing the name of the file whose status is to be retrieved.
* `statbuf`:`struct __old_kernel_stat*`[K] - Pointer to the previously allocated oldstat buffer structure into which the status information should be stored.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### old_stat
#### Type
Kprobe + Kretprobe
#### Purpose
To gather information on what files were requested and their stat information before they were found.

## Example Use Case
For example, an admin logging tool may want to log the oldstat calls of a process to get information about which files were accessed by it.

## Issues
The information obtained from this event is only valid at the time of retrieval, meaning that subsequent changes to the file might have been made and yield unexpected results. Furthermore, the user running the oldstat system call must have read access to the requested file.

## Related Events
The stat system call is a newer version of the oldstat system call and offers a more robust way of retrieving status about files. The syslog system call also provides information about files being accessed.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

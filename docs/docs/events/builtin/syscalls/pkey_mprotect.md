
# pkey_mprotect

## Intro
pkey_mprotect - change memory protections on a memory region protected by a protection key

## Description
pkey_mprotect is a Linux system call used to change the memory protection of a memory region so that it is bound to a protection key (pkey). As such, it works similarly to mprotect with the added protection that only if the application has the correct protection key it can access the memory region. This added security can be leveraged to protect copy-on-write functions and static data, allowing only authorized applications to access specific areas of process memory.

## Arguments
* `addr`:`void*`[U] - Pointer to the start of the memory region.
* `len`:`size_t`[U] - Size of the memory region to be changed.
* `prot`:`int`[U] - A combination of protection flags as described by the mprotect function.
* `pkey`:`int`[U] - The protection key associated with the memory region.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_pkey_mprotect
#### Type
Kprobe.
#### Purpose
Track calls to the `pkey_mprotect` syscall.

## Example Use Case
The most common use for the `pkey_mprotect` syscall is to protect copy-on-write functions from unauthorized use. A process can set a memory region protected by a specific protection key, thus only calls from processes with the correct key can access that region.

## Issues
No documented issues. 

## Related Events
The `pkey_alloc` syscall is used to allocate protection keys. The `pkey_free` syscall is used to programmatically free protection keys which are no longer in use.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# sched_getscheduler

## Intro
sched_getscheduler - Retrieve the scheduling policy of a process.

## Description
The sched_getscheduler() system call retrieves the scheduling policy currently applied to the process identified by pid. If pid is zero,
the policy of the calling process is retrieved. 

The available scheduling policies are:
* SCHED_OTHER - the traditional time-sharing policy.
* SCHED_FIFO   - a first-in, first-out policy
* SCHED_RR     - a round-robin policy

sched_getscheduler() can be used to retrieve the currently used scheduling policy for any process, allowing for the comparison of different policies for a process or for multiple processes.

## Arguments
* `pid`:`pid_t`[K] - The tid of the process whose policy will be retrieved.

### Available Tags
* K - Originated from kernel-space.

## Hooks
No functions are hooked in order to allow execution of the system call.

## Example Use Case
This system call can be used in the context of a monitoring application to compare scheduling policies between different processes.

## Issues
None.

## Related Events
The system call `sched_setscheduler` is related to this system call, as it allows for the setting of the scheduling policy for a process.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Trace<PERSON> recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# openat

## Intro 
openat - The `openat(2)` system call is used to open a file relative to a directory file descriptor.

## Description
`openat(2)` performs the same task as `open(2)`: it opens the file `pathname` (relative to the directory referred to by the file descriptor `dirfd`), but it can optionally provide the additional functionality of AT_SYMLINK_NOFOLLOW.

The arguments are similar to `open(2)`: the `pathname` of the file to be opened, a flags `flags` and a mode `mode` to set it if it is created. The only difference is that the `dirfd` argument is a file descriptor of the directory that will serve as a prefix of the pathname. This allows `openat(2)` to be used in a thread-safe sequence of operations that all operate relative to the same directory.

## Arguments 
* `dirfd`: `int`[K] - A file descriptor referring to the directory relative to which `pathname` is to be accessed. If `dirfd` is the special value `AT_FDCWD`, then `pathname` is relative to the current working directory.
* `pathname`: `const char*`[K,U] - A pathname of the file to be opened.
* `flags`: `int`[K] -File access mode and file status flags. This argument is a bit-mask created by ORing together the `O_RDONLY`, `O_WRONLY`, `O_RDWR`, `O_APPEND`, `O_CREAT`, `O_EXCL`, `O_NOCTTY`, `O_TRUNC`, `O_DSYNC`, `O_DIRECTORY`, `O_NOFOLLOW`, `O_SYMLINK`, `O_CLOEXEC` and `O_NONBLOCK` flags.
* `mode`: `mode_t`[K] -Mode of the created file. This argument must be supplied when `O_CREAT` is specified in flags. 

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_sys_open
#### Type
kprobe
#### Purpose
Hooked to analyze the system call parameters.

## Example Use Case
`openat()` can be used to open a file in a secure manner, given that two file descriptors are available. The intention of `openat()` is that it can be used in a thread-safe sequence of operations that all operate relative to the same directory. A simple example of this is ensuring that a file is opened in the same directory as its parent, and *not* in another directory, even if the working directory has been changed in the meantime.

## Issues
Due to the fact that `openat()` uses an internal buffer to store the pathname, there is a potential vulnerability to [TOCTOU (time of check, time of use)](https://en.wikipedia.org/wiki/Time_of_check_to_time_of_use) attacks.

## Related Events
`creat(2)`, `open(2)`, `stat(2)`

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

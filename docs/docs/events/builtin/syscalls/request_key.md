
# request_key

## Intro
request_key - create or update an object based on a given type, description and callout information.

## Description
request_key is a system call used to create an object of a given type, with a specified description, and callout information that could be used by a userspace program to process that object. The call will return a key serial, which is the id associated with the created object. The request key call can also be used to update an existing object; this is done by providing the same type and description, and an updated callout info, and the key serial of the object needing to be updated.

## Arguments
* `type`:`const char*`[K] - the type of the object.
* `description`:`const char*`[K] - description of the object.
* `callout_info`:`const char*`[K] - callout information that can be processed by an userspace program.
* `dest_keyring`:`key_serial_t`[K] - the key serial of the keyring that will receive the new/updated object (defaults to the current thread keyring).

### Available Tags
* K - Originated from kernel-space.

## Hooks
### request_key
#### Type
KProbe
#### Purpose
To trace and log the creation/update of an object passed to the request_key system call.

## Example Use Case
A use case example for request_key is an authentication system or encryption program. request_key can be called with the callout_info set to the user information and encrypted with the users public key, allowing the userspace application to verify the caller is the same user that encrypted the object.

## Issues
request_key could be vulnerable to TOCTOU attack, where the value of the object being updated is checked before the update and then changed before the update finishes its operations.

## Related Events
* keyctl_describe_key - get information about a given key.
* keyctl_delete_key - delete a key.
* keyctl_read - read the contents of a key.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

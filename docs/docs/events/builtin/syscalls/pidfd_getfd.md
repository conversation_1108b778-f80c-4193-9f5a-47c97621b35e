
# pidfd_getfd

## Intro
pidfd_getfd - opens a file descriptor of the opened process by using pidfd

## Description
The pidfd_getfd syscall will take a pidfd input as well as a target file descriptor 
number, targetfd. It will then open a file descriptor in the opened process 
associated with targetfd. If the flags argument is set to be zero, it will open 
the original file descriptor without any extra options. If set to O_CLOEXEC, 
it will open the target file descriptor with the FD_CLOEXEC flag set.

## Arguments
* `pidfd`:`int`[K] - File descriptor created by the pidfd_open syscall which references
a process that is already opened.
* `targetfd`:`int`[K] - File descriptor number to be opened in the already opened process.
* `flags`:`unsigned int`[K] - Flags with which the file descriptor should be opened.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### __x64_sys_pidfd_getfd
#### Type
KProbes + KRetProbes
#### Purpose
To capture and monitor processes via the pidfd_getfd syscall.

## Example Use Case
One useful application of pidfd_getfd is to monitor the progress of a process. 
By using it in conjunction with the pidfd_open syscall, it is possible to open 
a file descriptor that references a process and then use the pidfd_getfd syscall 
to open a file descriptor to the original file descriptor of the process. 
This can then be used in an auditd system to check the stats of the process and 
identify any potentially malicious activities.

## Issues
No known issues.

## Related Events
* pidfd_open - Used to open a file descriptor to a process
* pidfd_send_signal - Used to send a signal to a process opened via pidfd_open

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

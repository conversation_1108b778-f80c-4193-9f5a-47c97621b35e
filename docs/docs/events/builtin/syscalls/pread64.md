
# pread64

## Intro
pread64 - read up to count bytes from file descriptor fd at offset offset

## Description
The pread64() system call reads up to `count` bytes from file descriptor `fd` at offset `offset`. It is identical to [`read`](http://man7.org/linux/man-pages/man2/read.2.html) except for the checking for a file descriptor that supports `pread` and the `offset` parameter.

Unlike [`read`](http://man7.org/linux/man-pages/man2/read.2.html) the data is not read from the current file offset; instead, `offset` byteswithe counted from the start of the file.

If some bytes were read before and `O_APPEND` was not set on the file descriptor, subsequent reads are made from the position which was 'current' at the time pread64() has been called. If the file was open with [`O_APPEND`](http://man7.org/linux/man-pages/man2/open.2.html) set, the data will be read from the current end of file.

The pread64() system call does not change the file offset and it is not affected by whether or not `O_APPEND` is set on the file descriptor.

## Arguments
* `fd`:int - file descriptor of the file to be read
* `buf`:void*[K] - a pointer to a buffer in which the data should be stored
* `count`:size_t - the number of bytes to be read
* `offset`:off_t - offset from the start of the file

### Available Tags
* K - Originated from kernel-space

## Hooks

### sys_pread64

#### Type
Kprobe

#### Purpose
To trace reads from the start of a file.

## Example Use Case
A security audit might need to track activity that reads from the beginning of files. 

## Issues

None.

## Related Events

* [read](http://man7.org/linux/man-pages/man2/read.2.html)
* [pread2](http://man7.org/linux/man-pages/man2/pread.2.html)

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

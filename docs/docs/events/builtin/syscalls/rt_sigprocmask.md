
# rt_sigprocmask

## Intro
rt_sigprocmask - examine and change a signal mask

## Description
rt_sigprocmask is a system call used to examine and change a signal mask for a process. The signal mask is a set of signals used to control what signals are blocked and thus not delivered to a process. It also allows for the manipulation of the process' signal mask atomically (in one instruction). The rt_sigprocmask system call is a realtime version of the sigprocmask system call, and will preserve the realtime signals.

There are some edge cases to be aware of when using rt_sigprocmask. For example, if the sigsetsize parameter is greater than the size of a sigset_t (128 bytes on 64-bit architectures), then the call will fail with EINVAL.

## Arguments
* `how`:`int` - Specifies how the signal mask is to be changed. It is one of the following constants:
   * SIG_BLOCK: The resulting set is the union of the current set and the set argument.
   * SIG_UNBLOCK: The resulting set is the intersection of the current set and the complement of the set argument.
   * SIG_SETMASK: The resulting set is the set argument.
* `set`:`sigset_t*`[K] - Pointer to a signal set whose elements will be added (SIG_BLOCK) or deleted (SIG_UNBLOCK) from the current set.
* `oldset`:`sigset_t*` - Pointer to a signal set which will be filled with the old signal mask.
* `sigsetsize`:`size_t` - The size of the signal set *oldset* in bytes.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### do_rt_sigprocmask
#### Type
Kprobe
#### Purpose
This function is hooked to enable monitoring of the behavior of the rt_sigprocmask system call. With this hook, you can monitor when the system call is called and how long the call takes.

## Example Use Case
One example of where this event could be useful is in monitoring system calls for benchmarking applications. By monitoring the rt_sigprocmask system call you can measure how long this system call takes to execute, which can give an indication of how fast the overall application is.

## Issues
One issue to be aware of when using this system call is that it is vulnerable to a time-of-check-to-time-of-use (TOCTOU) race condition due to the fact that the signal mask can be changed between the time of the check and the time of the use.

## Related Events
* rt_sigaction()
* sigprocmask()

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

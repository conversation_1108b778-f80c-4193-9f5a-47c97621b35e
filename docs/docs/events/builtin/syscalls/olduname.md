
# olduname

## Intro
`olduname` - Returns sysname, nodename, release, version and machine information of the running system.

## Description
The `olduname` syscall is used to get basic system information about the running Linux system, such as its kernel version and the architecture version of a running machine. This is the original version of the uname system call and its information might be less accurate than the more modern version of the system call. It is commonly used to retrieve a string describing the name and version of the system, which is returned in the kernel parameter `buf`.

## Arguments
* `buf`:`struct utsname*`[K,U] - Pointer to a `struct utsname` passed by the user to be used to store the system information.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_olduname
#### Type
Kprobe
#### Purpose
Hooked to capture the system information for further investigation.

## Example Use Case
One example use of the `olduname` system call is to retrieve the Linux kernel version running when a process is spawned. This can be used as an additional source of information to detect malicious activities.

## Issues
Most current Linux distributions use the newer version of the system call, so the information gathered from `olduname` might be limited.

## Related Events
`uname` - newer version of the system call used to look for system information.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

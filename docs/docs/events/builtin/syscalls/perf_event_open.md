
# perf_event_open

## Intro
perf_event_open - Used to start/stop a specific performance monitoring event on a specific CPU

## Description
The `perf_event_open system call` gives userspace the ability to request very specific performance monitoring events for a specific CPU. This syscall can be used to start and stop monitoring events, depending on the flags set when the syscall is called. It can also be used to group multiple performance monitoring events into a single group and therefore provide a combined report. This syscall has a few drawbacks, such as being susceptible to TOCTOU (time of check, time of use) attacks, as the application may not always be able to guarantee the same data as when the check was performed.

## Arguments
* `attr`:`struct perf_event_attr*`[U] - User space memory buffer containing the definition of the performance event. The kernel will use this information to start/stop the performance event.
* `pid`:`pid_t`[K] - Process ID of the process where the event needs to be started or stopped. If set to ‘-1’, the event will be started or stopped on all processes (but not threads).
* `cpu`:`int`[K] - CPU number on which the event needs to be started or stopped. If set to ‘-1’, the event will be started or stopped on all CPUs.
* `group_fd`:`int`[U,K] - File descriptor associated with an event group (used to group multiple events into a single report). It is set to ‘-1’ if not part of an event group.
* `flags`:`unsigned long`[K] - Flags that indicate whether to start or stop the event.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### perf_event_open
#### Type
kprobe + kretprobe
#### Purpose
To collect performance events for a specific process, CPU or event group.

## Example Use Case
A user space application that needs to get detailed performance stats for one or multiple processes running on a CPU. This application can use the `perf_event_open` system call, with the appropriate attributes and flags, to get detailed performance stats for each process (or all processes, or a group of processes).

## Issues
The application may be vulnerable to TOCTOU (time of check, time of use) attacks, as the data may change after the check is performed.

## Related Events
* `perf_event_open`
* `perf_event_read`

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

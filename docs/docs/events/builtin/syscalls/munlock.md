
# `munlock`

## Intro
`munlock` - unlock memory from being in the locked state

## Description
The `munlock` syscall unlocks an area of memory previously locked using the `mlock` syscall. The range of memory that is unlocked must follow the same rules as for `mlock`, i.e., it must contain the entire range from `addr` to `addr + len` inclusively. Note that this syscall does not cause the kernel to allocate the pages that it unlocks, any previously allocated pages in the range are the ones that are unlocked.

`munlock` releases or removes the record lock memory range held by the process, allowing it to be swapped out.

## Arguments
* `addr`:`const void*`[K] - start address of the memory range to unlock.
* `len`:`size_t`[K] - Length of the memory range to unlock.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### Set_memory_x
#### Type
Kprobe + Kretprobe.
#### Purpose
To log when memory is unlocked.

## Example Use Case
The `munlock` syscall can be used to unlock areas of memory held in a locked state so that it can be swapped out and reclaimed. This could be used to ensure that the system is not continually locking and unlocking pages, thus avoiding unnecessary fragmentation of memory.

## Issues
None.

## Related Events
`mlock` - lock memory in memory so that it is not paged out.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

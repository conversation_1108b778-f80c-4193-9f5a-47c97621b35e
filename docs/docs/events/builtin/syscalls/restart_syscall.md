
# restart_syscall

## Intro
restart_syscall - Restarts a stopped system call that was previously interrupted by a signal

## Description
The `restart_syscall` system call restarts a previously suspended system call using the `SA_RESTART` flag supplied when setting a signal handler with `sigaction`. This system call restarts the call which was previously interrupted using `sigprocmask` and allows execution to continue where it left off. This system call is useful in situations where a signal was handled by a signal handler, such as when a child process is killed, but without restarting the system call, execution could be left in an inconsistent state.

## Arguments
* `None`

### Available Tags
* None

## Hooks
### sys_restart_syscall
#### Type
Kprobe
#### Purpose
Tracing restarted system calls to monitor the impact of signals in the system.

## Example Use Case
Tracing restarted system calls to detect a system call that is taking too long to complete, or to determine how long it takes to restart a system call.

## Issues
There may be some system calls which cannot be restarted, and in those cases the `restart_syscall` system call may fail.

## Related Events
* [sigaction](https://man7.org/linux/man-pages/man2/sigaction.2.html)
* [sigprocmask](https://man7.org/linux/man-pages/man2/sigprocmask.2.html)

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

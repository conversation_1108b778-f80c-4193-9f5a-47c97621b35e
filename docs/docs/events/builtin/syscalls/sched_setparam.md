
# sched_setparam

## Intro
sched_setparam - sets parameters for a process in the scheduling process table

## Description
Sched_setparam is a Linux system call that sets parameters for a given process 
in the scheduling process table. It changes the scheduling policy and parameters 
associated with a given process. The scheduling policy is not changed, however the 
parameters used for the selected policy are set for the process in question. This 
helpful for defining more fine-grained scheduling controls.

When a process has its scheduling parameters modified it can affect how the CPU 
performs. It can also affect the way that a process interacts with users, which 
can have an effect on user experience.

There can be drawbacks to using sched_setparam, as setting the right schedules 
parameters are quite complex and depend on a variety of factors, such as other 
running processes, the architecture, and so on.

## Arguments
* `pid`:`pid_t`[K] - This argument specifies the process ID of the process that 
the scheduling parameters will be set to.
* `param`:`struct sched_param*`[U] - This argument specifies a pointer to a 
struct sched_param, which contains the scheduling parameters to be set.
  
### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_sched_setparam
#### Type
Kprobe
#### Purpose
To log information about processes that are executing the do_sched_setparam 
system call.

### sched_setscheduler
#### Type
Kretprobe
#### Purpose
To capture the return value of the sched_setparam call in order to determine if the 
scheduling parameters have been set successfully.

## Example Use Case
One use case for this system call is using sched_setparam to set a certain CPU 
scheduling priority for a certain process. This could be done if a process is 
causing issues with other processes or user experience and needs to be given a 
lower priority in order to allow other processes to run.

## Issues
There can be a number of issues when using sched_setparam. The parameters set in 
the struct sched_param can be specific to a certain architecture, so the values 
need to be set correctly for the current architecture. Also, the parameters can be 
complex to set correctly, as they can depend on other running processes or the 
architecture.

## Related Events
* sched_getscheduler - retrieves the scheduling policy of the process with given pid
* sched_getparam - retrieves the scheduling parameters associated with a given process.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

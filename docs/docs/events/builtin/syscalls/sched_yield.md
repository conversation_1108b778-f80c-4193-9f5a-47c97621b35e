
# sched_yield

## Intro
sched_yield - temporarily pause the current thread in favor of other threads

## Description
The task sched_yield() cause the calling thread to relinquish the CPU. The thread is put to the end of the queue for its static priority and another thread will be scheduled to run. If there are no other threads running in the system, the yield will cause the thread to continue to run.

sched_yield() is useful for an application to allow other threads to run (i.e. to prevent starvation of other threads) or for a thread to be scheduled in to reduce the latency for an scheduling event.

## Arguments
No arguments for this syscall.

## Hooks
### do_sys_sched_yield
#### Type
Tracepoint
#### Purpose
To monitor context switches.

## Example Use Case
sched_yield() should be used when a process needs to pause its execution and give a chance to the other threads, waiting in the same priority queue.

## Issues
If there are no runnable threads with the same priority level, then the current thread will not be context switched and all other threads will be stuck behind it.

## Related Events
* sched_setscheduler: to change the scheduling policy and priority of selected thread.
* sched_get_priority_max/min: to get the highest/lowest priority limit of a scheduling policy.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of <PERSON><PERSON> recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# landlock_create_ruleset

## Intro
`landlock_create_ruleset` - create a security ruleset

## Description
`landlock_create_ruleset` creates a Landlock security ruleset. This system call allows the caller to specify the attributes of the new ruleset — what memory objects can be affected by the ruleset, read/write permissions, and other properties of the ruleset — as well as the size of the ruleset, and any flags that should be applied. The returned ruleset is private to the calling process, and should not be shared with (or communicated to) other processes. 

There are a few edge-cases to be aware of when creating rulesets. If a ruleset is created with too low of a size, then it won't be able to hold all of the possible rules that the caller might request. Additionally, a ruleset created with higher size than needed would waste system resources. It's also possible for two or more processes to interfere with each other by attempting to create overlapping rulesets, which could result in policy-level violations.

## Arguments
* `attr`:`struct landlock_ruleset_attr*`[U] - pointer to the Landlock attribute for the new ruleset. Contains options like the target object and access permissions.
* `size`:`size_t`[U] - the size of the new ruleset, in bytes.
* `flags`:`u32`[K] - flags determining optional features when creating the ruleset.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### __x64_sys_landlock_create_ruleset
#### Type
Kprobe + Kretprobe
#### Purpose
Hooks the landlock_create_ruleset function so that its arguments can be processed and the ruleset can be monitored.

### __do_landlock_create_ruleset
#### Type
Kprobe
#### Purpose
Hooks the do_landlock_create_ruleset kernel entry point so that the arguments and their values can be inspected.

## Example Use Case
`landlock_create_ruleset` can be used as part of a security policy enforcement system. By creating a ruleset with the appropriate access permissions, an application can ensure that processes in its security domain can only access the memory objects that are necessary for their work and no additional objects.

## Issues
Due to the fact that Landlock rulesets are private to the calling process, it's possible for processes to interact in unexpected ways and interfere with each other by attempting to create overlapping rulesets. Care should be taken to ensure that Landlock rulesets are sufficiently large and are properly scoped so that they do not cause any policy-level violations. Additionally, since this system call relies on user-space pointers, it is vulnerable to TOCTOU attacks.

## Related Events
* landlock_init - initialize landlock
* landlock_apply - apply ruleset to specified memory object
* landlock_remove - remove ruleset from specified memory object

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

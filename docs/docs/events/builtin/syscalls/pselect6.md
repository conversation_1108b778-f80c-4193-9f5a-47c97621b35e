
# pselect6

## Intro
pselect6 - the Linux system call that syntactically combines select() and pselect() system calls.

## Description
pselect6 is the Linux system call that syntactically combines select() and pselect() system calls. This system call can be used to select or monitor many files or sockets for readability, writability, prior-to-termination conditions etc. It can also optionally monitor a set of signal numbers for presence in the caller's signal mask without temporarily replacing the signal mask.

The pselect6 system call takes 6 parameters. The first parameter is an integer, which specifies the maximum of the number of file descriptors in any of the three sets (readfds, writefds, exceptfds). The second, third and fourth parameters are pointers to sets of file descriptors (readfds, writefds, exceptfds). The fifth argument is a pointer to a timeval structure which specifies the absolute or relative timeout time. The sixth argument is a pointer to a signal set, or a null pointer, which specify the set of signals to be monitored for any pending signal during the monitor time.

Once pselect6 is called, it will monitor all the file descriptors which are in the three sets until a file descriptor becomes ‘ready’ or a timeout occurs or a signal becomes ‘pending.’ When one of these events happens, the pselect6 returns to the caller with the ready filed descriptors and updated signal masks.

## Arguments
* `nfds`:`int`[K] - an integer specifying the maximum number of file descriptors in the set of files to monitor.
* `readfds`:`fd_set*`[K] - The file descriptor set to monitor for readability.
* `writefds`:`fd_set*`[K] - The file descriptor set to monitor for writability.
* `exceptfds`:`fd_set*`[K] - The file descriptor set to monitor for exceptional conditions.
* `timeout`:`struct timespec*`[U] - Pointer to a time structure which specifies the absolute or relative timeout time.
* `sigmask`:`void*`[K] - Pointer to a signal set or NULL. If not NULL, specifies the set of signals to be monitored for any pending signal during the monitor wait. If a signal is pending, pselect6 will return with an EINTR error code and will fill the sigmask parameter with the set of pending signals.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value).

## Hooks
### do_syscall_64
#### Type
Kprobe
#### Purpose
To monitor for calls to the system call related to pselect6.

## Example Use Case
pselect6() system call can be used to monitor multiple sockets for readability, writeability, exceptions, or other conditions simultaneously. For example, a web server could use pselect6 to wait for incoming connections on a set of sockets, and have one single process to handle all of the sockets.

## Issues
The pselect6 system call is vulnerable to time of check/ time of use (TOCTOU) race condition because the process might have blocked on the selector before the I/O that it is waiting for will complete.

## Related Events
* select
* sigprocmask

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

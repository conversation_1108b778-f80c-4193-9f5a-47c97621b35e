
# query_module

## Intro
query_module - query the kernel for information about a loaded Linux Kernel Module

## Description
The query_module system call provides information about loaded Kernel Modules and information about those modules. It can be used to query for the module's size, symbol table, and other related information. The call is typically used by kernel debuggers and kernel developers, as well as system administrators who need to check the module's state. 

## Arguments
* `name`:`char*`[K] - name of the module to query.
* `which`:`int`[K] - Parameter specifying the module information to return.
* `buf`:`void*`[K, U] - data buffer to store the queried information in.
* `bufsize`:`size_t`[K] - size of the data buffer.
* `ret_size`:`size_t*`[K, U] - pointer to size_t store the size of the returned information.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### query_module
#### Type
Kprobe
#### Purpose
This system call is often used by kernel debuggers and developers for obtaining information about Linux modules. As this call is highly involved in kernel development and debugging, it is important to be aware of when and how it is being used. Kprobes can be used to intercept the call in order to observe its parameters and behaviour.

## Example Use Case
One scenario where query_module can be useful is for trouble-shooting issues with Linux modules. System administrators may use this system call to manually query the kernel for information on a module, in the event that module is not behaving correctly.

## Issues
There is a potential TOCTOU (Time of Check, Time of Use) vulnerability with query_module. As this system call has the potential to access user-space memory, it is possible for malicious code to overwrite the contents of the queried module before it is used. It is therefore recommended to use this system call with caution and only in well-controlled situations.

## Related Events
* init_module - initialize a Linux Kernel Module
* delete_module - delete a Linux Kernel Module
* get_kernel_syms - query the kernel for exported symbols.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

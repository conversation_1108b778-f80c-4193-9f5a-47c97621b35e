
# Preadv2

## Intro
Preadv2 - read data from a file descriptor into multiple memory areas at a given position.

## Description
Preadv2 reads **iovcnt** buffers from the file associated with the file descriptor **fd** into the structures pointed to by **iov**. The **pos_l** and **pos_h** arguments are taken together to form a 64-bit value that specifies the location in the file (offset) from which the data is to be read. By using `preadv2` the position of the file descriptor is left unchanged. The flags argument is a bit mask composed of the bitwise OR of zero or more of the following commands.

## Arguments
* `fd`: `int` - File descriptor for the file to read from.
* `iov`: `const struct iovec*` - Pointer to an array of `iovec`  structures pointing to the buffer where the data should be read into.
* `iovcnt`: `unsigned long` - Number of elements in the iov buffer.
* `pos_l`: `unsigned long` - Lower 32-bits of the 64-bit offset value.
* `pos_h`: `unsigned long` - Higher 32-bits of the 64-bit offset value.
* `flags`: `int` - Bitmask of options describing whether `preadv2` or `preadv2` should be used.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_preadv2
#### Type
kprobe + fentry.
#### Purpose
To monitor the syscalls related to reading data from the file descriptor.

## Example Use Case
Preadv2 can be used to read from a large file into multiple smaller buffers. It is useful for copying files, as seek and read don't need to be called for each read. It is also useful for applications that want to read a lot of data from a file in parallel.

## Issues
The flags argument is not yet supported in glibc.

## Related Events
* preadv
* pwritev2

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# readdir

## Intro
readdir - Reads a directory structure from the given opened file descriptor

## Description
The readdir() system call reads a single directory entry from a directory opened with the open() system call. It returns the next directory entry in a directory stream pointed to by the file descriptor fd. Upon successful return, the content of the directory entry is copied to the old_linux_dirent structure pointed to by dirp.

The readdir() system call is used to get information about each subdirectory in a filesystem, one entry at a time. It is possible to call this system call on any open file descriptor , regardless of the type of file is associated with it.

The count argument specifies the length of the old_linux_dirent buffer. If the buffer size is not large enough to hold the entire directory entry, an error will be returned.

readdir() does not guarantee that successive calls to this system call with the same file descriptor will always return the entries in the same order.

## Arguments
* `fd`:`unsigned int`[K] - A file descriptor returned from open system call.
* `dirp`:`struct old_linux_dirent*`[K] - A pointer to the structure.
* `count`:`unsigned int`[K] - Total number of bytes for the structure.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### readdir()
#### Type
Kprobes
#### Purpose
Monitoring calls to readdir.

## Example Use Case
readdir() can be used to monitor a specific directory in the filesystem. It can be used to monitor or detect any changes or operations, such as the creation of files or the deletion of directories, in the monitored directory.

## Issues
readdir() is vulnerable to a Time of Check, Time of Use (TOCTOU) race condition, which can result in security vulnerabilities if not carefully handled.

## Related Events
open(), seekdir(), getdents(), rewinddir()

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

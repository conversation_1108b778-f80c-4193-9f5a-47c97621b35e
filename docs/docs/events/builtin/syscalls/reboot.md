
# reboot

## Intro
reboot - reboot a computer

## Description
The reboot() system call causes the system to be rebooted. The magic parameter
and magic2 parameter, if non-zero, are passed to the kernel and the behavior of
the call can depend on their values. The cmd parameter may be one of the
following:

* RB_AUTOBOOT - Forces the system to restart, no questions asked.
* RB_HALT_SYSTEM - Halts the system after the system has been shut down.
* RB_POWER_OFF - Powers the system off and is the equivalent of using the power
  button on a PC.

The arg parameter can be set to a pointer to a character array, which is used to
provide additional information such as a shutdown message, but it is generally
ignored.

## Arguments
* `magic`:`int` - Magic argument. Used to tell the kernel certain privileges
  for the reboot procedure.
* `magic2`:`int`-[K] - Magic argument number two. Used to tell the kernel
  certain privileges for the reboot procedure.
* `cmd`:`int` - Command to execute. Can be one of the following: RB_AUTOBOOT,
  RB_HALT_SYSTEM and RB_POWER_OFF.
* `arg`:`void*`-[U] - A pointer to a character array that can be used to
  provide additional information but is generally ignored.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)

## Hooks
### do_sys_reboot
#### Type
Kprobe
#### Purpose
To intercept attempts to reboot the system.

## Example Use Case
Reboot the system to apply a kernel patch or a system configuration change.

## Issues
The reboot system call does not return, so it needs to be used with caution to
avoid inadvertently affecting system stability.

## Related Events
* sys_reboot - Generates a system reboot.
* sys_restart_syscall - Restarts a system call after it was interrupted by a
  signal.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# open

## Intro
open - is a system call used to open the file specified by its filename

## Description
The purpose of open is to provide a system call to logicaly open a file in the file system.  
It might be important to keep in mind that the open call does not actually access the file in the system.  
Using the flags parameter, one could get different types of access rights to the file.  
One of the drawbacks of using open is that it is vulnerable to TOCTOU (time of check, time of use).

## Arguments
* `pathname`:`const char*`[K] - The path to the file we want to open.
* `flags`:`int`[K] - Flags for the access. Used to set the access/permissions for the call.
* `mode`:`mode_t`[K] - Control access to the opened file.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_open
#### Type
Kprobe
#### Purpose
To record every open system call, including its associated arguments.

## Example Use Case
This event could be used to monitor reads and writes to a specific file.

## Issues
open could be very vulnerable to race-condition issues, as it is vulnerable to TOCTOU (Time Of Check, Time Of Use).

## Related Events
The related events for the open syscall are __close()__, __read()__, and __write()__.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

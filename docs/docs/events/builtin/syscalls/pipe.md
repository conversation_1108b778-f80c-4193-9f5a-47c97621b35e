
# pipe

## Intro
pipe - creates a pipe, a unidirectional data channel that can be used for interprocess communication

## Description
Pipe is a system call that creates a unidirectional data channel for communication between two file descriptors.pipefd is an array of two integers, used to return two file descriptors referring to the ends of the pipe. The first file descriptor is for reading; the second file descriptor is for writing.

Pipes are used for communication between two related processes, allowing them to exchange data between them. Pipes have the advantage of allowing data to be passed between the processes without having to be stored in memory. The limitation is that the data sent through a pipe can only be read once.

As pipe() is a system call, it is vulnerable to TOCTOU attacks. This is because the file descriptor can be obtained on a pipe call and can be used until the pipe is closed.

## Arguments
* `pipefd`:`int[2]`[K] - Used to return two file descriptors referring to the ends of the pipe. The first file descriptor is for reading; the second file descriptor is for writing.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_pipe
#### Type
Static analysis / kprobe
#### Purpose
Hooked to monitor communication between processes, detect suspicious activity and detect TOCTOU vulnerability. 

## Example Use Case
Pipe is used when two separate processes need to communicate. However, it can also be used as a debugging tool, by monitoring the data that is being passed through a pipe.

## Issues
The main issue with pipe is that the data can only be read once. This can cause issues when the reading process fails to read all of the data and it is discarded.

## Related Events
* read
* write
* select

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# mq_timedsend_time32

## Intro
mq_timedsend_time32 - sends message on a message queue with a timeout value

## Description
The mq_timedsend_time32() system call sends the message pointed to by *u_msg_ptr, with message length *msg_len, and a message priority of *msg_prio, to the queue indicated by the message queue descriptor *mqdes.

The u_abs_timeout parameter points to a structure of type "old_timespec32", specifying the absolute timeout (time of day) at which the system call should expire - in other words, the time to wait for a successful return before aborting the operation. If the value of u_abs_timeout->tv_nsec is greater than or equal to 1 billion, the system call fails and returns an error.

When a process sends a message to a queue, the process that is blocked on a message queue due to either mq_timedsend_time32() or mq_timedreceive_time32() will wake up, and either receive the message if the process is blocked on mq_timedreceive_time32(), or find that the send operation succeeded if the process is blocked on mq_timedsend_time32().

The tm_timedsend_time32() system call provides an interface for sending a message to an existing message queue with an absolute timeout, preventing an indefinite wait, as it can happen if the message queue is full and the receiver side is absent.

## Arguments
* `mqdes`:`mqd_t`[K] - message queue descriptor.
* `u_msg_ptr`:`char*`[KU] - pointer to the message to be sent.
* `msg_len`:`unsigned int`[K] - length of the message.
* `msg_prio`:`unsigned int`[K] - message priority.
* `u_abs_timeout`:`struct old_timespec32*`[K] - pointer to the absolute timeout value.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_mq_timedsend_time32
#### Type
kprobe
#### Purpose
To trace mq_timedsend_time32 system call.

## Example Use Case
mq_timedsend_time32 could be used to monitor and trace an application sending messages to a message queue with a timeout value.

## Issues
The use of absolute timestamps might lead to incorrect time values.

## Related Events
mq_timedreceive_time32

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# mremap

## Intro
mremap - move a mapping and resize.

## Description
The mremap() system call changes the mapping between virtual addresses and physical pages existing in a process address space. It expands or shrinks the area of existing mapping without moving the page frames. The old address range may have multiple non-contiguous mappings, and new address range can be in a non-contiguous mapping as well.

mremap() can also be used to map a non-contiguous area of physical memory as a contiguous mapping. The `MREMAP_FIXED` flag should be used to request that the new mapping is made at a fixed address, which is specified in the last argument.

Aside from enlarging or shrinking a mapping the old range and the new range may overlap or not be adjacent. In those cases, mremap() can also be used to move the mapping from one address space to another.

The flags argument enables several options for the mremap() operation. It determines whether the new range of memory should be accessible, what must happen if the desired new address range already contains a mapping, whether the mapping is private or shared, and whether the old address range should be unshareable (prevented from being transparently cloned/shared). 

mremap() is not required to check access permissions on the old address range before performing the remapping, so care should be taken to ensure that the old address range is correctly protected.

## Arguments
* `old_address`:`void*`[KU] - The virtual address of the existing mapping.
* `old_size`:`size_t`[K] - The size of the existing mapping.
* `new_size`:`size_t`[K] - The size of the new mapping.
* `flags`:`int`[K] - The flags controlling the behavior of the mremap() system call.
* `new_address`:`void*`[KUOPT] - The virtual address of the new mapping. When the `MREMAP_FIXED` flag is used, this argument specifies the new address to which the existing mapping is moved, otherwise it is ignored.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_mremap
#### Type 
Kprobes + kretprobes
#### Purpose
This function was hooked to monitor memory allocation and deallocation.

### do_mremap
#### Type 
Kprobes
#### Purpose
This function was hooked to monitor inputs and outputs to the sys_mremap syscall.

## Example Use Case
mremap() can be used to apply a new mapping to an area of physical memory. This might be useful, for example, in an embedded system to remap a shared memory space, allowing different tasks to access the same physical memory in different address spaces.

## Issues
mremap() is vulnerable to TOCTOU (Time Of Check Time Of Use) attacks. If an attacker can create or modify a mapping between the moment a mapping is checked and the moment it is used, it can increase the attack surface area of the application.

## Related Events
* mmap
* munmap
* memcpy
* memmove

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

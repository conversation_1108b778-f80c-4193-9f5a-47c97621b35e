
# sched_setscheduler

## Intro
sched_setscheduler - Set scheduling policy and parameters of a process

## Description
sched_setscheduler can be used to set the scheduling policy and scheduling parameter of an existing process with specified process ID (pid) and specified scheduling parameters (param). It can be used to set and toggle between different scheduling policies, e.g. SCHED_FIFO, SCHED_RR, SCHED_NORMAL, etc., depending on the requirements of the process. It can also be used to set the process' priority within the specified scheduling policy, using the struct sched_param.

This call is subject to the CAP_SYS_NICE capability.

## Arguments
* `pid`:`pid_t`[K] - Process ID of the process whose scheduling policy should be set.
* `policy`:`int`[K] - Scheduling policy. Must be one of SCHED_OTHER (normal time-sharing), SCHED_FIFO (run until blocked or killed), SCHED_RR (same as FIFO but timesliced), or SCHED_BATCH (batch job).
* `param`:`struct sched_param*`[K] - Scheduling parameters, to set the process priority within the specified scheduling policy.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### sched_setscheduler
#### Type
Kprobe
#### Purpose
To track the scheduling policy and parameters being set for a process.

## Example Use Case
sched_setscheduler can be used when one needs fine-grained control over how a process is scheduled on a system. It can be used to prioritize processes, for example, when one process demands more resources than other processes.

## Issues

Before 4.15, scheduling parameters did not work with SCHED_IDLE or SCHED_BATCH.

## Related Events
* sched_getparam - Get scheduling parameters of a process.
* sched_getaffinity - Get/set the CPU affinity of a process.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

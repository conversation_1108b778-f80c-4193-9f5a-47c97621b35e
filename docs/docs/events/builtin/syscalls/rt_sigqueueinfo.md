
# rt_sigqueueinfo

## Intro
rt_sigqueueinfo - send a signal with associated data to a thread

## Description
The `rt_sigqueueinfo()` system call sends a signal, `sig`, and associated
data, `info`, to the thread identified by `tgid`. This call is like `kill()`,
but allows to pass the extra data.

Compared to `sigqueue()`, the `info` is passed as a pointer rather than an
argument.

The `siginfo_t`data type typically includes signal information such as signal
number and sender information. 

In  kernels prior to 2.6.9, the `info` argument is limited to a `si_value` value,
which indicate the signal value.

Using `rt_sigqueueinfo` has some advantages:
* It can pass the additional data, such as `si_value`, for signal information.
* Its use is thread-safe.

## Arguments
* `tgid`:`pid_t`[K] - thread group identifier for which signal is to be sent.
* `sig`:`int`[U] - signal to be sent.
* `info`:`siginfo_t*`[U] - signal info.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_sigaction
#### Type
Kprobe
#### Purpose
To get the signal information before being sent.

## Example Use Case
An example use case of rt_sigqueueinfo is when an application needs to send a
signal with data from kernel-space to a thread in user-space.  It can be used
to report information about a critical event or abort a process.

## Issues
* The signal info (`siginfo_t`) is limited to a `si_value` value for kernels
  prior to 2.6.9.
* The third argument, `info`, is a pointer to user space. Therefore, information
  from user space might not be reliable, as it might be subject to TOCTOU
  attacks.

## Related Events
* `kill()` - send a signal to a process.
* `sigqueue()` - send a signal with data to a process or thread.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

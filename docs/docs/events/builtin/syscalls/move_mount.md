
# move_mount

## Intro

move_mount - reposition an existing mount in the file hierarchy.

## Description

The `move_mount()` system call is designed to allow the repositioning of an
existing mount point to a new location within the file system hierarchy.

This system call is a part of the revamped mount API introduced in Linux 5.2 and
provides a more flexible and secure way to move mounts, particularly beneficial
within user namespaces.

Unlike the traditional `mount()` system call with the `MS_MOVE` flag,
`move_mount()` offers a more granular approach to mount operations.

## Arguments

* `from_dfd`:`int`[K] - File descriptor referring to the directory containing the mount point to be moved.
* `from_pathname`:`const char *`[U] - Pathname relative to `from_dfd` indicating the mount to move.
* `to_dfd`:`int`[K] - File descriptor referring to the directory where the mount point should be moved to.
* `to_pathname`:`const char *`[U] - Pathname relative to `to_dfd` indicating the target location for the mount.
* `flags`:`unsigned int`[K] - Flags to modify the behavior. As of now, the flag value is typically set to 0.

### Available Tags

* K - Originated from kernel-space.
* U - Originated from user space.
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use).
* OPT - Optional argument - might not always be available (passed with null value).

## Hooks

### sys_move_mount

#### Type

Tracepoint (through `sys_enter`).

#### Purpose

To observe and trace when the `move_mount()` system call is invoked, capturing
details about the source and target mount locations.

## Example Use Case

Enhancing containerization tools or sandboxing solutions by reorganizing the
mount namespace for better isolation or to achieve specific filesystem layouts
without resorting to the older, less granular `mount()` syscall.

## Issues

Misuse of the `move_mount()` system call can lead to unintended changes in the
file hierarchy, potentially affecting system stability or introducing security
vulnerabilities. Proper validation and checks are essential when utilizing this
syscall.

## Related Events

* `open_tree()` - Open a reference to a mountpoint for use with the new mount API operations.
* `mount_setattr()` - Adjust attributes of a mount.

> This document was automatically generated by OpenAI and reviewed by a Human.

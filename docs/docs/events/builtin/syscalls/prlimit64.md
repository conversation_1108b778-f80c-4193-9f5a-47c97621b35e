
# prlimit64

## Intro
prlimit64 - manipulate resource limits of a process

## Description 
The prlimit64() system call is used to both set and get the resource limits of a process. It is similar to the setrlimit and getrlimit functions but it can handle larger numerical values when specifying a resource limit. Resource limits can be set per user, and are a mechanism to prevent processes from using toomuch memory, growing too large, creating too many threads or processes, or creating files that are too large. It is important to note that this system call only changes the limits in the current process and not in any other processes that belong to the same user.

There are some edge cases to be aware of. It is possible to set a limit that is lower than the current limit. In this case, the limit will remain the same. It is also possible to set a limit that is lower than the current usage. In this case, the process will be terminated. Finally, it is not possible to set a limit for another user using this system call.

## Arguments
* `pid`:`pid_t` - the process ID of the process to change the limit for. If set to 0, it will default to the current process.
* `resource`:`int` - the resource whose limit is being set/gotten. Examples are RLIMIT_AS (address space size of the process), RLIMIT_CORE (maximum size of core file that process can create), and RLIMIT_NOFILE (maximum number of open files). 
* `new_limit`:`const struct rlimit64*` - a pointer to a rlimit64 structure which contains the new limit value to set.
* `old_limit`:`struct rlimit64*` - optionally, a pointer to a rlimit64 structure which the current limit will be copied into. 

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_prlimit64
#### Type
Kprobe + Kretprobe
#### Purpose
To track when the resource limits of a process are set and getting

## Example Use Case
A process monitoring system could use this event to mark when the resource limits of a process are changed in order to be aware of when a process is requesting more resources 

## Issues
Since the new and old limit are stored in user-space provided pointers, they are vulnerable to TOCTOU attacks.

## Related Events
sys_setrlimit / getrlimit - the 32-bit versions of the same functions.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# sched_getaffinity

## Intro
sched_getaffinity - retrieve the CPU affinity of a process

## Description
The sched_getaffinity() system call retrieves the CPU affinity mask of the process whose ID is specified in pid, and places it in the cpusetsize bytes of memory pointed to by mask. 

The CPU affinity mask is a bit mask where each bit represents a CPU that the process is able to execute on. A set bit signifies that a CPU is able to be used while an unset bit signifies that the CPU is unavailable. 
The argument cpusetsize is the size of the mask pointed to by mask and must not be smaller than the size of the kernel's internal CPU mask, returned by sysconf(_SC_NPROCESSORS_ONLN).

This system call is useful for optimizing the process's compatibility when switching between systems with different numbers of processors.

## Arguments
* `pid`:`pid_t`[U] - process ID of the thread whose affinity is to be retrieved.
* `cpusetsize`:`size_t`[U] - number of bytes in the bitmask pointed to by mask.
* `mask`:`unsigned long*`[U] - pointer to a bit mask for the CPUs on which the thread may run.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_sched_getaffinity()
#### Type
Kprobe
#### Purpose
To monitor the state of CPU affinity for different threads. 

## Example Use Case
An example of a use case for this system call would be to ensure that a user's thread or process is only running on one CPU or multiple CPUs across different physical processors. 

## Issues
The most common issue with this system call is when the size of the mask pointed to by mask is not sufficient to contain the kernel's internal CPU mask, leading to truncation of the actual CPU mask.

## Related Events
* sched_setaffinity(): Set the CPU affinity mask of a process

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

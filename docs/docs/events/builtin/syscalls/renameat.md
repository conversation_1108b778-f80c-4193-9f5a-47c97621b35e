
# renameat

## Intro
renameat - rename a file or directory, relative to an existing file descriptor

## Description
The `renameat` syscall is used to rename a file or directory, relative to an existing file descriptor. It takes four arguments: two file descriptors, `olddirfd` and `newdirfd`, and two file paths, `oldpath` and `newpath`. `olddirfd` and `newdirfd` must refer to valid file descriptors associated with directories, while `oldpath` and `newpath` must refer to valid file paths. If these arguments are not provided, the `renameat` syscall will fail with an error.

This syscall is used for managing files and directories. It is useful for providing an extra level of protection when renaming files and directories, because it allows the user to specify a file descriptor rather than a full path. It also allows for easier, more portable file and directory management.

Note that the `renameat` syscall is vulnerable to TOCTOU (time of check, time of use) issues, because the file path and file descriptor can become invalid between the check and the use.

## Arguments
* `olddirfd`:`int`[K] - the file descriptor of the directory containing the file to be renamed. Must be a valid file descriptor associated with a directory.
* `oldpath`:`const char*`[K] - the path of the file to be renamed, relative to the directory referenced by `olddirfd`. Must be a valid file path.
* `newdirfd`:`int`[K] - the file descriptor of the directory in which to rename the file. Must be a valid file descriptor associated with a directory.
* `newpath`:`const char*`[K] - the path of the new file, relative to the directory referenced by `newdirfd`. Must be a valid file path.

### Available Tags
* K - Originated from kernel-space.

## Hooks
### [__x32_sys_renameat](https://elixir.bootlin.com/linux/latest/source/fs/compat.c#L945)
#### Type
Kprobes
#### Purpose
To trace the `renameat` syscall.

## Example Use Case
A user wants to move a file from one directory to another, without knowing the full file paths. The user can use the `renameat` syscall to move the file, using the two file descriptors to refer to the respective directories.

## Issues
The `renameat` syscall is vulnerable to TOCTOU issues. This can cause unexpected behavior if the file path or file descriptor becomes invalid between the check and the use.

## Related Events
* [openat](openat.md) - open a file relative to an existing file descriptor.
* [unlinkat](unlinkat.md) - delete a file or directory, relative to an existing file descriptor.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# select

## Intro
select - Wait for some event on a group of files or sockets

## Description
The select event is used to monitor changes on a set of sockets or files. It 
allows for the asynchronous detection of changes on these objects in a 
time-efficient manner. After calling select, the application will be notified 
when any of the objects in the set changes and is able to take action 
accordingly. Select also supports waiting for a timeout.

## Arguments
* `nfds`:int - The highest numbered file descriptor in any of the sets and should be one more than the descriptor of the last file or socket that the select() statement is being asked to watch.
* `readfds`:fd_set* - Optional argument. A pointer to an fd_set data structure passed by reference. The set of file descriptors whose status the caller wishes to query.
* `writefds`:fd_set* - Optional argument. A pointer to an fd_set data structure passed by reference. The set of file descriptors on which the caller is asking to be notified when it is possible to write data.
* `exceptfds`:fd_set* - Optional argument. A pointer to an fd_set data structure passed by reference. The set of file descriptors which the system will report when there is an exceptional condition pending.
* `timeout`:struct timeval* - Optional argument. A pointer to a timeval structure containing the maximum time for which the caller wishes to wait for the select() call to complete before returning. 

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_select
#### Type
Kprobe
#### Purpose
Hooked in order to detect when the kernel is awaiting on a select call.

## Example Use Case
The select event can be used for an application to keep track of multiple users concurrently without having to wait for each action by every user. This can be done by calling select on multiple sockets to maintain a listen client and detect when one of the clients has sent data.   

## Issues
When calling select with more than one objects, if the data in one object changes, the application will have to wait additional time to serve the other objects in the set. This can lead to longer wait times if one of the objects in the set has data which changes rapidly or is frequently use accessed. 

## Related Events
* epoll - event-driven I/O model which uses events instead of the select/poll APIs 
* poll - provides much of the same functionality as select.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

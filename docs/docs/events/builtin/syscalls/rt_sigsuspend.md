
# rt_sigsuspend

## Intro
rt_sigsuspend - temporarily replace the signal mask and wait for a signal

## Description
The rt_sigsuspend() system call is used to temporarily replace the signal mask of the calling process and wait for a signal to be delivered. It is a layer on top of the sigprocmask() system call which atomically performs the operations of replacing the signal mask and suspending execution of the calling thread. It is mostly used in signal handlers when temporarily blocking the signal the handler is responding to.

The main advantage of this system call is that it does not require the user to pass the current blockmask when temporarily replacing it.

However, there are some drawbacks when using rt_sigsuspend(). Firstly, the process resumes execution from the same address it was interrupted in (as opposed to other system calls such as sigsuspend() which resumes execution from the start of the signal handler). This can lead to the atomicity issues when signals are delivered while the process is trying to do several operations, leading to inconsistent states. Secondly, it can only be used in signal handlers and is not appropriate for other synchronisation tasks.

## Arguments
* `mask`:`sigset_t*`[K, U] - pointer to user space memory which holds the signal mask to be replaced.
* `sigsetsize`:`size_t`[K] - size of the mask in bytes.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)

## Hooks
### do_rt_sigsuspend
#### Type
Kprobe
#### Purpose
Tracing when the rt_sigsuspend system call is executed. 

## Example Use Case
Tracing when a process invokes the rt_sigsuspend system call.

## Issues
None.

## Related Events
* sigprocmask - get blocked signals or set blocked signals
* sigsuspend - suspend process until signal

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

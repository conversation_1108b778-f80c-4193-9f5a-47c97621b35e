
# ppoll_time32

## Intro
The `ppoll_time32` system call is used to poll the given file descriptor sets with the given timeout value in a Linux-based system.

## Description
The `ppoll_time32` system call is used to poll for activity on a set of file descriptors specified by an array of `struct pollfd` structures. The call will wait for a specified timeout period, given in an old Linux `struct old_timespec32` structure, for activity on any of the descriptors to occur and then return. If the `sigmask` pointer is non-null, the call will block all signals in the given sigset while polling.

The call will return the number of file descriptors selected or zero if the timeout expired. It will return `-1` and set `errno` in case of errors.

## Arguments
* `ufds`:`struct pollfd*`[KU] - Pointer to an array of `struct pollfd` structures containing the file descriptors, a list of events and a list of returned events.
* `nfds`:`unsigned int`[K] - The number of items in the array of `struct pollfd` structures.
* `tsp`:`struct old_timespec32*`[K] - Pointer to a `struct old_timespec32` containing the timeout period.
* `sigmask`:`sigset_t*`[K] - Pointer to a `sigset_t` containing the list of signals to block while polling.
* `sigsetsize`:`size_t`[K] - Size of the given sigset.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user-space (for example, pointer to user space memory used to get it).
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use).
* OPT - Optional argument - might not always be available (passed with null value).

## Hooks
### do_ppoll
#### Type
kretprobe.
#### Purpose
Hooks the `do_ppoll` function to trace the `ppoll_time32` system call. 

## Example Use Case
The `ppoll_time32` system call can be used in a multi-threaded application to safely query the status of a large number of file descriptors.

## Issues
The `ppoll_time32` system call is vulnerable to TOCTOU (time of check, time of use) attacks. This means that an attacker may be able to manipulate the content of the `struct pollfd` structures between the time they are checked by the `ppoll_time32` system call and the time they are actually used.

## Related Events
* `poll_time32` - Similar system call used for polling file descriptors.
* `ppoll` - Similar modern system call used for polling file descriptors.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

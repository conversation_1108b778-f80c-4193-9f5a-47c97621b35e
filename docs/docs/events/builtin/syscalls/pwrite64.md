
# pwrite64

## Intro
pwrite64 - A system call that writes data from a buffer described by `buf` of a given size `count` to a given file descriptor `fd` at a certain offset `offset` in the open file.

## Description
The `pwrite64` system call, like the `write` system call, writes data from a buffer described by `buf` of a given size `count` to a specified file descriptor `fd`. The `pwrite64` system call differs from the `write` system call in that it writes a specified number of bytes to an open file descriptor at an offset instead of the current position. This offset can be specified with `offset`, a parameter of type `off_t`, which is a signed integer integer type frequently used to specify an offset or distance.This behavior is useful if, for example, an application needs to write data to a file at specific locations.

The `pwrite64` system call is not vulnerable to Time-of-Check Time-of-Use (TOCTOU) because the parameters are evaluated before any action is taken.

## Arguments
* `fd`:`int`[K] - The file descriptor.
* `buf`:`const void*`[KU] - Pointer to the buffer containing data for write operation. 
* `count`:`size_t`[K] - Quantity of data, in bytes, to be written to the file descriptor.
* `offset`:`off_t`[K] - Specifies the position in the file where the data issued from `buf` should be written. 

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys\_pwrite64
#### Type
Kprobe
#### Purpose
Used to keep track of all calls to `pwrite64` system call.

### do\_syscall_64
#### Type
Kprobe 
#### Purpose
Used to observe the start of all the execution of group 2 system calls.

## Example Use Case
The `pwrite64` system call can be used by programs that need to modify an existing file in a very precise way. A good example would be a text editor that uses pwrite64 to modify the file at a particular byte offset, ensuring that it keeps the original file structure intact.

## Issues
Due to its parameters, the `pwrite64` system call can be rather slow when dealing with large files since its read/write operations occur at an offset specified in the parameter `offset`; hence, it performs one operation at a time (as opposed to `write` system call).

## Related Events
* `write`
* `pwritev64`

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# readlinkat

## Intro
readlinkat - read the contents of a symbolic link

## Description
The readlinkat() system call is used to read the contents of a symbolic link. The call requires a file descriptor for the directory that contains the symbolic link, the name of the link itself, a buffer for the link contents, and the size of the buffer. The call returns the contents of the link in the given buffer, or an error code if the buffer is too small for the link.

The readlinkat() system call is similar to the readlink() system call, except it allows for more control of the environment in which the link is read. While readlink() always takes the path of the link as relative to the current working directory, readlinkat() allows the path to be relative to a directory that is different than the current working directory. 

The readlinkat() system call has some drawbacks. It is vulnerable to "time of check, time of use" (TOCTOU) race conditions, as the link could be changed between the time it is checked and when it is used. It also requires the user to provide a valid file descriptor for the target directory. This can be difficult when the target directory is a remote filesystem, or a virtual filesystem.

## Arguments
* `dirfd`:`int`[K] - file descriptor for directory containing the link.
* `pathname`:`const char*`[KU] - the pathname of the link.
* `buf`:`char*`[KU] - buffer for the link contents. 
* `bufsiz`:`int`[K] - size of the buffer.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### vfs_readlink()
#### Type
Kprobes
#### Purpose
To add additional tracing capabilities to the readlinkat() system call.

### generic_permission()
#### Type
Kretprobes
#### Purpose
To record the return value of the readlinkat() system call.

## Example Use Case
The readlinkat() system call can be used to read the contents of a symbolic link and ensure that the link points to the expected target. This can be useful for validation of the directory structure, or for creating a log of file and directory changes.

## Issues
The readlinkat() system call is vulnerable to "time of check, time of use" (TOCTOU) race conditions, as the link could be changed between the time it is checked and when it is used. 

## Related Events
The readlink() system call is the simplified version of readlinkat() and is used to read the contents of a link. The lstat() system call can be used to check the properties of the link and ensure it is a valid link. The openat() system call is used to open the target directory and get a file descriptor for use with readlinkat().

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# rt_sigreturn

## Intro
rt_sigreturn - System call to return from user-owned signal handlers

## Description
The `rt_sigreturn` system call restores the caller's user-space context previously saved by the kernel at the occurrence of a signal. It is used on architectures that don't store the user-space registers (in particular context) when a signal handler is invoked by the kernel, so the user-space has to do it instead. After restoring the context, the `rt_sigreturn` call will return to the address the execution was at before the signal happened. 

It's important to note that returning from a signal handler or restarting a system call instead of executing a `sigreturn` call may be a source of bugs which could lead to privilege escalation or other vulnerabilites. In addition, due to the race conditions which are present due to the nature of the signal handling in Linux, it is vulnerable to TOCTOU attacks as noted below.

## Arguments
* `ustack`:`pointer`[KU] - Pointer to the user-space signal stack context. The signal stack context is defined by each architecture.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)

## Hooks
### sys_rt_sigreturn
#### Type
Trampoline
#### Purpose
To capture the context when a signal handler exits.

## Example Use Case
An example use-case for `rt_sigreturn` is for tracing signal handler return paths, for example for debugging purposes or for catching vulnerabilities. All the user-space registers and the stack content used when the signal handler was called can be obtained from the `ustack` argument. This data could be used in order to detect if a bug or an exploit has been triggered. 

## Issues
`rt_sigreturn` can be used in an attempt of privilege escalation if an attacker can find a way to modify the `ustack` argument which is only present in kernel-space. In addition, `rt_sigreturn` is vulnerable to TOCTOU (time of check, time of use) attacks, since it can be interrupted before the operation is completely finished.

## Related Events
* signal - system call used to send signals to processes.
* signal handler - signal handlers are functions called by the kernel when a signal is delivered.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

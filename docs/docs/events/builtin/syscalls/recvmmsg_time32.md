
# recvmmsg_time32

## Intro
recvmmsg_time32 system call - an implementation for recvmmsg for 32-bit timeval structures.

## Description
The recvmmsg_time32() system call is an implementation of the recvmmsg() system call that uses the old_timespec32 data type instead of timespec. This allows the receiving of multiple messages with a single system call, instead of multiple calls. The old_timespec32 structure allows a 32-bit seconds and microseconds fields, as opposed to the timespec structure, which uses a 64-bit nanoseconds field.

This function is using pointers to user space memory, to fetch multiple messages, which could be potentialy vulnerable to TOCTOU (Time Of Check, Time Of Use) attacks.

## Arguments
* `fd`:`int`[KU] - The file descriptor from which to receive the message.
* `mmsg`:`struct mmsghdr*`[KU] - A pointer to an array of mmsghdr structures containing information about the messages that are to be received by recvmsg().
* `vlen`:`unsigned int`[K] - The length of the array of mmsghdr structures.
* `flags`:`unsigned int`[K] - The flags to be used for the recvmsg() syscalls used in the process.
* `timeout`:`struct old_timespec32*`[K] - A pointer to a struct old_timespec32 containing the timeout for the recvmmsg call to wait for incoming messages.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### tracepoint_sys_enter_recvmmsg_time32
#### Type
Tracepoint
#### Purpose
To trace calls to the recvmmsg_time32 syscall.

### ptrace_recv_syscall
#### Type
Ptrace hook
#### Purpose
To be able to hook the recvmmsg_time32 syscall, and modify its arguments before syscall enter.

## Example Use Case
This system call can be used to receive multiple messages in one system call, for example for an event-based server application. It can be used to wait in a select()-like loop on multiple sockets, but instead of having to perform multiple recvmsg() calls, a single recvmmsg_time32() system call can be used to receive messages from all the sockets.

## Issues
TBD

## Related Events
* recvmsg,
* sendmsg,
* select,
* poll,
* epoll_wait

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

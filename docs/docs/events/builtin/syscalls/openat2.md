
# openat2

## Intro
openat2 - create a file with special options

## Description
The openat2() system call function creates a file and returns a file descriptor for it. It is similar to openat(), but allows more detailed control over the file's creation. The possible combination of how->flags and how->writes can be used to customize the properties of the file, like its permissions, ownership, and timestamps. The openat2() syscall also allows us to specify a size when creating the file, which is useful for creating files with non-zero size or preallocation.

## Arguments
* `dirfd`:`int`[K] - a file descriptor for a directory.
* `pathname`:`const char*`[U] - a pointer to the pathname of the file to be opened, within the directory referred to by the dirfd argument.
* `how`:`struct open_how*`[K] - a pointer to the structure describing how the file should be opened.
* `size`:`size_t`[K] - the requested size of the opened file.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### openat2
#### Type
TRAP
#### Purpose
The openat2 system call is used to create a file. It is hooked to instrument the creation of files.

### sys_openat2
#### Type
KRETPROBE 
#### Purpose
The sys_openat2 system call is the kernel entrypoint for the openat2 system call. It is hooked to intercept and validate the arguments of the call before execution.

## Example Use Case
openat2 can be used to create a hardlink to a file. By providing a pointer to a struct open_how as the "how" argument, we can create a link to a file, with specified ownership and permissions.

## Issues
Due to the nature of this system call, there is the risk of TOCTOU vulnerabilities. Care must be taken to make sure that the parameters are valid when they are checked and still valid when they are used.

## Related Events
openat(), creat(), link(), unlink()

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.


# pselect6_time32

## Intro
pselect6_time32 - select a subset of file descriptors based on given time frame

## Description
This event is a Linux version of `select` system call. It allows user to select subset of file descriptors, or sockets, withing a given time frame. The time frame is defined by the last argument, a timeout structure.

The `pselect6_time32` system call is used to wait for a given set of file descriptors to become ready to perform read, write orupdate operations, or to get a notification when one of the specified set of signals arrives within given time frame.

## Arguments
* `n`:`int` - maximum number of file descriptors to select from.
* `inp`:`fd_set*` - pointer to set of file descriptors that are being tested for readiness.
* `outp`:`fd_set*` - pointer to a set of file descriptors that are set if they become ready.
* `exp`:`fd_set*` - pointer to a set of file descriptors that will be set if an error occurs.
* `tsp`:`struct old_timespec32*`[KU] - pointer to a timeout structure.
* `sig`:`void*`[K] - pointer to set of signals to be watched.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)

## Hooks
### do_select_vec
#### Type
Probes 
#### Purpose
Log the arguments given to pselect6_time32 for troubleshooting.

### compat_sys_pselect6_time32
#### Type
In-kernel hooking 
#### Purpose
To hook calls to the pselect6_time32 system call in kernel and log the arguments for further analysis.

## Example Use Case
Let's say we have a whole set of file descriptors that have to be open in order to execute our program. However, the conditions under which they can be opened are limited, and we would like to know if all the file descriptors are ready to use within the given time frame. To do this, we can use `pselect6_time32` to wait for all the file descriptors to be ready, and provides us a subset of the file descriptors that are actually ready. On failure, the `exp` set contains file descriptors that couldn't be ready within the timeout.

## Issues
The last argument, `sig`, is designed to work as a synchronous equivalent of `sigevent`, which can be used to execute asynchronous operations on signals. As of version 5.4 of the Linux kernel, this argument is not implemented and have to be passed as null or ignored.

## Related Events
* `select` - related event with wider compatiblity, but no support for timeout.
* `pselect6` - similar to `pselect6_time32` except the timeout structure takes `old_timespec` instead of `old_timespec32` format.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

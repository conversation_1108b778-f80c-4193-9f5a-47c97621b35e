
# rename

## Intro
rename - change the name or location of a file

## Description
The `rename()` syscall is used to change the name or location of a file. It can also be used to move a file from one directory to another, or to rename a file. This can be important for system processes, for example making changes to files during updates or installation processes, or for application processes like renaming a file with a new version number.  Edge cases can arise from differing permissions levels between the two paths. It can also be vulnerable to TOCTOU (time of check, time of use) attacks if not properly handled, as the operation of the rename can be interrupted between the check that the file exists and when the rename happens.

## Arguments
* `oldpath`: `const char*`[K] - Path to existing file
* `newpath`: `const char*`[K] - New path of file

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_rename
#### Type
Kprobe
#### Purpose
Monitoring the rename syscall

## Example Use Case
An example use-case of the `rename()` syscall could be updating a program. A program would call `rename()` to move an existing version of the file to a new directory and/or with a different name, and then place a new version of the program into the proper directory.

## Issues
TOCTOU (Time of Check, Time of Use) attacks are possible when using the `rename()` syscall. If a file is checked to exist and then renamed, an attacker can theoretically create the file in the meantime and cause an attack vector through the rename.

## Related Events
The `rename()` syscall is often used in conjunction with other syscalls, such as `open()`, `stat()`, `unlink()` or `mkDIR()`. Additionally, other related syscalls like `link()` and `symlink()` can achieve similar effects.

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

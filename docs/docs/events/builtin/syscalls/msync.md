
# msync

## Intro
msync - synchronize a file with a memory map

## Description
The msync() system call causes all changes made to the in-core copy of a file 
mapped by a mapping handled by the "msync" call to be written to the storage device 
on which the file resides or, depending on the flags, to be discarded. 

Advantages of using msync are that is allows to synchronize a region of a file to 
with a copy in a memory mapped region. This can help to avoid data corruption issues
when a program exits abruptly.

Drawbacks of msync can be related to the performance of synchronizing the data. 
Synchronous updates could hurt performance in scenarios where high throughput is 
desired.

## Arguments
* `addr`:`void*`[K] - Pointer to the start of the memory region. Must be aligned to the 
system page size.
* `length`:`size_t`[K] - Size of the memory region in bytes.
* `flags`:`int`[K] - Flags for how to look for changes. 
  
### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### sys_msync
#### Type
Kprobe
#### Purpose
Track when and with what parameters the sys_msync syscall is called.

## Example Use Case
A client-server application where the server keeps track of state using a file and
the clients operate on that file by reading and writing to it without calling msync.
A msync event could be used to check when the clients write to the file and whether
they do it correctly or not.

## Issues
msync only works with files that have been directly mapped in to physical memory. 
This means it can't be used with files that are mapped in to virtual memory.
  
## Related Events
* sys_munmap
* sys_mprotect
* sys_mlock

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

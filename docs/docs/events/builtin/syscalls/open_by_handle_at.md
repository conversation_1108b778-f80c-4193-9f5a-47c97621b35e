
# open_by_handle_at 

## Intro 
open_by_handle_at - open a file by a handle, based on a users data

## Description 
The `open_by_handle_at()` system call is used to open a file given a file handle and mount point. The `open_by_handle_at()` call is similar to `openat()`, except that the former takes a file handle obtained from `name_to_handle_at()` instead of a pathname string.

The `open_by_handle_at()` provides a flexible and fast way of creating new file descriptors for an existing file without the overhead of a file system lookup. It is, however, more fragile than `openat()` since the target file system may not be available in the same state when it is used for the later operation.

## Arguments 
* `mount_fd`:`int`[K] - file descriptor of the directory at which to open the handle. 
* `handle`: `struct file_handle*`[U] - pointer to the file handle that was acquired by the `name_to_handle_at` system call.
* `flags`: `int`[K] - file status flags to use when opening the handle. This can be used to control the permissions of the file being opened. 

### Available Tags 
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### do_sys_open
#### Type
Kprobes + ftrace
#### Purpose
Hooks are used to detect when the `open_by_handle_at()` syscall is called by userspace

### get_empty_filp
#### Type
Kprobes + ftrace
#### Purpose
Hooks are used to detect when the `open_by_handle_at()` syscall is setting up the file descriptor.

## Example Use Case 
The `open_by_handle_at()` system call can be used to open files from a remote network system without needing to obtain the path of the file.

## Issues 
This system call is vulnerable to TOCTOU (time of check, time of use) attacks as the file handle may have been acquired from a different file system that may not be in the same state when the file is opened.

## Related Events 
* `openat()` - Open a file relative to a directory file descriptor
* `name_to_handle_at()` - Convert a file name to handle

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

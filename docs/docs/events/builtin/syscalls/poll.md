
# poll

## Intro
poll() - waits for one of a set of file descriptors to become ready to perform I/O operations.

## Description
The poll() system call is a method of multiplexing a process across several input-output operations. It allows a process to wait for events or data to become available on one or more files or file-descriptors before continuing execution. 

Certain edge-cases can cause blocking and other issues. Since the process is attempting to read multiple file descriptors, if one of them is blocked, the entire system call may become blocked as a result. Also since the poll() syscall relies on the kernel to inform of changes in the files, there is potential for TOCTOU (Time Of Check, Time Of Use) attacks.

## Arguments
* `fds`:`struct pollfd*`[U] - Array of pollfd  data structures.
* `nfds`:`unsigned int`[U] - Number of file descriptors contained in fds.
* `timeout`:`int`[OPT] - The timeout, in milliseconds. A timeout value of 0 causes poll to return immediately, while a value of -1 means an infinite timeout.

### Available Tags
* K - Originated from kernel-space.
* U - Originated from user space (for example, pointer to user space memory used to get it)
* TOCTOU - Vulnerable to TOCTOU (time of check, time of use)
* OPT - Optional argument - might not always be available (passed with null value)

## Hooks
### syscall_entry_poll
#### Type
kprobe
#### Purpose
Monitor the entry to the poll syscall in order to capture the arguments passed in.

## Example Use Case
In a system which needs to monitor multiple file descriptors for events, poll() can be used instead of continually checking each file descriptor to be ready for I/O operations.

## Issues
The biggest issue is that of TOCTOU attacks, since the kernel can itself be exploited to modify the information on the file descriptor before the poll syscall ends.

## Related Events
select(), epoll(), muxed poll()

> This document was automatically generated by OpenAI and needs review. It might
> not be accurate and might contain errors. The authors of Tracee recommend that
> the user reads the "events.go" source file to understand the events and their
> arguments better.

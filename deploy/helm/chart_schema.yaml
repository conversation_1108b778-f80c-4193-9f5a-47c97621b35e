name: str()
home: str(required=False)
version: str()
appVersion: any(str(), num(), required=False)
description: str(required=False)
keywords: list(str(), required=False)
sources: list(str(), required=False)
maintainers: list(include('maintainer'), required=False)
dependencies: list(include('dependency'), required=False)
icon: str(required=False)
engine: str(required=False)
condition: str(required=False)
tags: str(required=False)
deprecated: bool(required=False)
apiVersion: str()
kubeVersion: str(required=False)
type: str(required=False)
annotations: map(str(), str(), required=False)
---
maintainer:
  name: str(required=False)
  email: str(required=False)
  url: str(required=False)
---
dependency:
  name: str()
  version: str()
  repository: str()
  condition: str(required=False)
  tags: list(str(), required=False)
  enabled: bool(required=False)
  import-values: any(list(str()), list(include('import-value')), required=False)
  alias: str(required=False)
---
import-value:
  child: str()
  parent: str()

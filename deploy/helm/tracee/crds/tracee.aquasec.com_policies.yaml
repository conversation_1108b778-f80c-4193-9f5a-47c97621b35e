---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.0
  name: policies.tracee.aquasec.com
spec:
  group: tracee.aquasec.com
  names:
    kind: Policy
    listKind: PolicyList
    plural: policies
    singular: policy
  scope: Cluster
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: tracee policy spec
            properties:
              defaultActions:
                items:
                  type: string
                type: array
              rules:
                items:
                  description: Rule is the structure of the rule in the policy file
                  properties:
                    actions:
                      items:
                        type: string
                      type: array
                    event:
                      type: string
                    filters:
                      items:
                        type: string
                      type: array
                  required:
                  - event
                  type: object
                type: array
              scope:
                items:
                  type: string
                type: array
            required:
            - rules
            - scope
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true

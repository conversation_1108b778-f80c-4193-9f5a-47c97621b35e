---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tracee
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tracee
subjects:
  - kind: ServiceAccount
    name: tracee
    namespace: {{ .Release.Namespace }}
{{- if .Values.operator.create }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tracee-operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tracee
subjects:
  - kind: ServiceAccount
    name: tracee-operator
    namespace: {{ .Release.Namespace }}
{{- end }}

{{- if .Values.operator.create -}}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "tracee-operator.fullname" . }}
  labels:
    {{- include "tracee.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ include "tracee-operator.fullname" . }}
  template:
    metadata:
      labels:
        app: {{ include "tracee-operator.fullname" . }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "tracee-operator.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
      - name: tracee-operator
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        command:
          - /tracee/tracee-operator
        args:
          - --health-probe-bind-address
          - {{ .Values.operator.healthProbeBindAddress }}
        env:
          - name: TRACEE_NAME
            value: {{ include "tracee.fullname" . }}
          - name: TRACEE_NAMESPACE
            value: {{ .Release.Namespace }}
        securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
        readinessProbe:
          httpGet:
            path: /healthz
            port: {{ trimPrefix ":" .Values.operator.healthProbeBindAddress }}
      {{- with .Values.operator.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.operator.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.operator.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

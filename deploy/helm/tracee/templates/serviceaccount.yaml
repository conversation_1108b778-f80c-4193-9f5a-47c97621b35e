{{- if .Values.serviceAccount.create -}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "tracee.serviceAccountName" . }}
  labels:
    {{- include "tracee.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
{{- if .Values.operator.create }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.operator.serviceAccount.name }}
  labels:
    {{- include "tracee.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}

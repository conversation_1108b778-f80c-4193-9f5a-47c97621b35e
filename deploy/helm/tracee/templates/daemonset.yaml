---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ include "tracee.fullname" . }}
  labels:
    {{- include "tracee.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "tracee.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "tracee.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "tracee.serviceAccountName" . }}
      hostPID: {{ .Values.hostPID }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: tracee
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - /tracee/tracee
          args:
            - --config
            - /tracee/config.yaml
          {{- if .Values.webhook }}
            - --output
            - webhook:{{ .Values.webhook }}
          {{- end }}
          env:
            - name: LIBBPFGO_OSRELEASE_FILE
              value: /etc/os-release-host
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- if .Values.config.healthz }}
          readinessProbe:
            httpGet:
              path: /healthz
              port: {{ trimPrefix ":" .Values.config.listenAddr }}
          {{- end }}
          volumeMounts:
            - name: tmp-tracee
              mountPath: /tmp/tracee
            - name: etc-os-release
              mountPath: /etc/os-release-host
              readOnly: true
            - mountPath: /var/run/containerd/containerd.sock
              name: containerd-sock
              readOnly: true
            - mountPath: /var/run/crio/crio.sock
              name: crio-sock
              readOnly: true
            - mountPath: /var/run/docker.sock
              name: docker-sock
              readOnly: true
            - mountPath: /var/run/podman/podman.sock
              name: podman-sock
              readOnly: true
            - name: tracee-config
              readOnly: true
              mountPath: /tracee/config.yaml
              subPath: config.yaml
            {{- range .Values.extraWebhookTemplates }}
            - name: tracee-templates
              readonly: true
              mountPath: "/tracee/templates/{{ .name }}"
              subPath: {{ .name }}
            {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: tmp-tracee
          hostPath:
            path: /tmp/tracee
        - name: etc-os-release
          hostPath:
            path: /etc/os-release
        - name: containerd-sock
          hostPath:
            path: /var/run/containerd/containerd.sock
        - name: crio-sock
          hostPath:
            path: /var/run/crio/crio.sock
        - name: podman-sock
          hostPath:
            path: /var/run/podman/podman.sock
        - name: docker-sock
          hostPath:
            path: /var/run/docker.sock
        - name: tracee-config
          configMap:
            name: tracee-config
        {{- if .Values.extraWebhookTemplates }}
        - name: "tracee-templates"
          configMap:
            name: tracee-templates
        {{- end }}

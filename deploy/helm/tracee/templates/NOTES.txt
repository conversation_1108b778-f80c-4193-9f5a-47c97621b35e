Trace<PERSON> has been successfully installed as a set of pods scheduled on each Kubernetes cluster
node controlled by the `{{ include "tracee.fullname" . }}` DaemonSet in the `{{ .Release.Namespace}}` namespace.
By default, threat detections are printed to the standard output of each pod:

$ kubectl logs -f daemonset/{{ include "tracee.fullname" . }} -n {{ .Release.Namespace }}

{"timestamp":1677676197900822209,"threadStartTime":1677676197713685267,"processorId":0,"processId":441,"cgroupId":7408,"threadId":441,"parentProcessId":431,"hostProcessId":4741,"hostThreadId":4741,"hostParentProcessId":4729,"userId":0,"mountNamespace":4026532733,"pidNamespace":4026532734,"processName":"dpkg","hostName":"app-75ff449bcd-","containerId":"ae95a93ce0c2f1b824ac4b205e4d1a3c8f724d663626c1ad23576efec64318de","containerImage":"docker.io/library/ubuntu:latest","containerName":"app","podName":"app-75ff449bcd-hfwnz","podNamespace":"default","podUID":"0f7ad251-39da-44f7-af28-34a0e971be3e","podSandbox":false,"eventId":"6029","eventName":"New executable dropped","matchedScopes":1,"argsNum":1,"returnValue":32768,"syscall":"","stackAddresses":null,"contextFlags":{"containerStarted":true,"isCompat":false},"args":[{"name":"path","type":"const char *","value":"/usr/bin/strace.dpkg-new"}],"metadata":{"Version":"1","Description":"An Executable file was dropped in the system during runtime. Container images are usually built with all binaries needed inside. A dropped binary may indicate that an adversary infiltrated your container.","Tags":null,"Properties":{"Category":"defense-evasion","Kubernetes_Technique":"","Severity":2,"Technique":"Masquerading","external_id":"T1036","id":"attack-pattern--42e8de7b-37b2-4258-905a-6897815e58e0","signatureID":"TRC-1022","signatureName":"New executable dropped"}}}

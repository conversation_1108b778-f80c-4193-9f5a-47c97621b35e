---
# Source: tracee/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tracee
  labels:
    helm.sh/chart: tracee-0.23.1
    app.kubernetes.io/name: tracee
    app.kubernetes.io/instance: tracee
    app.kubernetes.io/version: "0.23.1"
    app.kubernetes.io/managed-by: Helm
---
# Source: tracee/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tracee-operator
  labels:
    helm.sh/chart: tracee-0.23.1
    app.kubernetes.io/name: tracee
    app.kubernetes.io/instance: tracee
    app.kubernetes.io/version: "0.23.1"
    app.kubernetes.io/managed-by: Helm
---
# Source: tracee/templates/tracee-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tracee-config
  labels:
    helm.sh/chart: tracee-0.23.1
    app.kubernetes.io/name: tracee
    app.kubernetes.io/instance: tracee
    app.kubernetes.io/version: "0.23.1"
    app.kubernetes.io/managed-by: Helm
data:
  config.yaml: |-
    cache:
        type: mem
        size: 512
    perf-buffer-size: 1024
    healthz: true
    metrics: true
    pprof: false
    pyroscope: false
    listen-addr: :3366
    log:
        level: info
    output:
        json:
          files:
            - stdout
        options:
            parse-arguments: true
            stack-addresses: false
            exec-env: false
            exec-hash: dev-inode
            sort-events: false
---
# Source: tracee/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tracee
rules:
- apiGroups:
  - apps
  resources:
  - daemonsets
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - tracee.aquasec.com
  resources:
  - policies
  verbs:
  - get
  - list
  - watch
---
# Source: tracee/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tracee
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tracee
subjects:
  - kind: ServiceAccount
    name: tracee
    namespace: default
---
# Source: tracee/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tracee-operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tracee
subjects:
  - kind: ServiceAccount
    name: tracee-operator
    namespace: default
---
# Source: tracee/templates/daemonset.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: tracee
  labels:
    helm.sh/chart: tracee-0.23.1
    app.kubernetes.io/name: tracee
    app.kubernetes.io/instance: tracee
    app.kubernetes.io/version: "0.23.1"
    app.kubernetes.io/managed-by: Helm
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: tracee
      app.kubernetes.io/instance: tracee
  template:
    metadata:
      labels:
        app.kubernetes.io/name: tracee
        app.kubernetes.io/instance: tracee
    spec:
      serviceAccountName: tracee
      hostPID: true
      securityContext:
        {}
      containers:
        - name: tracee
          image: "docker.io/aquasec/tracee:0.23.1"
          imagePullPolicy: IfNotPresent
          command:
            - /tracee/tracee
          args:
            - --config
            - /tracee/config.yaml
          env:
            - name: LIBBPFGO_OSRELEASE_FILE
              value: /etc/os-release-host
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          securityContext:
            privileged: true
          readinessProbe:
            httpGet:
              path: /healthz
              port: 3366
          volumeMounts:
            - name: tmp-tracee
              mountPath: /tmp/tracee
            - name: etc-os-release
              mountPath: /etc/os-release-host
              readOnly: true
            - mountPath: /var/run/containerd/containerd.sock
              name: containerd-sock
              readOnly: true
            - mountPath: /var/run/crio/crio.sock
              name: crio-sock
              readOnly: true
            - mountPath: /var/run/docker.sock
              name: docker-sock
              readOnly: true
            - mountPath: /var/run/podman/podman.sock
              name: podman-sock
              readOnly: true
            - name: tracee-config
              readOnly: true
              mountPath: /tracee/config.yaml
              subPath: config.yaml
          resources:
            {}
      tolerations:
        - effect: NoSchedule
          operator: Exists
        - effect: NoExecute
          operator: Exists
      volumes:
        - name: tmp-tracee
          hostPath:
            path: /tmp/tracee
        - name: etc-os-release
          hostPath:
            path: /etc/os-release
        - name: containerd-sock
          hostPath:
            path: /var/run/containerd/containerd.sock
        - name: crio-sock
          hostPath:
            path: /var/run/crio/crio.sock
        - name: podman-sock
          hostPath:
            path: /var/run/podman/podman.sock
        - name: docker-sock
          hostPath:
            path: /var/run/docker.sock
        - name: tracee-config
          configMap:
            name: tracee-config
---
# Source: tracee/templates/deploy.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tracee-operator
  labels:
    helm.sh/chart: tracee-0.23.1
    app.kubernetes.io/name: tracee
    app.kubernetes.io/instance: tracee
    app.kubernetes.io/version: "0.23.1"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tracee-operator
  template:
    metadata:
      labels:
        app: tracee-operator
    spec:
      serviceAccountName: tracee-operator
      securityContext:
        {}
      containers:
      - name: tracee-operator
        image: "docker.io/aquasec/tracee:0.23.1"
        imagePullPolicy: IfNotPresent
        command:
          - /tracee/tracee-operator
        args:
          - --health-probe-bind-address
          - :8081
        env:
          - name: TRACEE_NAME
            value: tracee
          - name: TRACEE_NAMESPACE
            value: default
        securityContext:
            privileged: true
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8081
---
# Source: tracee/templates/default-policy.yaml
apiVersion: tracee.aquasec.com/v1beta1
kind: Policy
metadata:
  name: default-policy
  annotations:
    description: traces default events
spec:
  scope:
    - global
  rules:
    - event: stdio_over_socket
    - event: k8s_api_connection
    - event: aslr_inspection
    - event: proc_mem_code_injection
    - event: docker_abuse
    - event: scheduled_task_mod
    - event: ld_preload
    - event: cgroup_notify_on_release
    - event: default_loader_mod
    - event: sudoers_modification
    - event: sched_debug_recon
    - event: system_request_key_mod
    - event: cgroup_release_agent
    - event: rcd_modification
    - event: core_pattern_modification
    - event: proc_kcore_read
    - event: proc_mem_access
    - event: hidden_file_created
    - event: anti_debugging
    - event: ptrace_code_injection
    - event: process_vm_write_inject
    - event: disk_mount
    - event: dynamic_code_loading
    - event: fileless_execution
    - event: illegitimate_shell
    - event: kernel_module_loading
    - event: k8s_cert_theft
    - event: proc_fops_hooking
    - event: syscall_hooking
    - event: dropped_executable
    - event: container_create
    - event: container_remove

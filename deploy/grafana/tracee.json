{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "liveNow": false, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 5, "x": 0, "y": 0}, "id": 10, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.4", "targets": [{"datasource": {"type": "prometheus", "uid": "w5C9dFs7k"}, "exemplar": true, "expr": "tracee_rules_detections_total", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Detections", "transparent": true, "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 5, "x": 5, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.4", "targets": [{"datasource": {"type": "prometheus", "uid": "w5C9dFs7k"}, "exemplar": true, "expr": "tracee_rules_signatures_total", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Signatures Loaded", "transparent": true, "type": "stat"}, {"description": "The rate of events processed and lost", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 18, "w": 13, "x": 10, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "w5C9dFs7k"}, "exemplar": true, "expr": "rate(tracee_ebpf_events_total[10s])", "interval": "", "legendFormat": "events/sec", "refId": "events/sec"}, {"datasource": {"type": "prometheus", "uid": "w5C9dFs7k"}, "exemplar": true, "expr": "rate(tracee_ebpf_lostevents_total[10s])", "hide": false, "interval": "", "legendFormat": "lost_events/sec", "refId": "lost_events/sec"}], "title": "Events Throughput", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 10, "x": 0, "y": 5}, "id": 6, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.4", "targets": [{"datasource": {"type": "prometheus", "uid": "w5C9dFs7k"}, "exemplar": true, "expr": "tracee_ebpf_events_total", "interval": "", "legendFormat": "", "refId": "A"}], "title": "EBPF Events Produced", "transparent": true, "type": "stat"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 9, "w": 10, "x": 0, "y": 10}, "id": 8, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "right"}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "w5C9dFs7k"}, "exemplar": true, "expr": "tracee_ebpf_lostevents_total", "interval": "", "legendFormat": "<PERSON><PERSON><PERSON> Lost Events", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "w5C9dFs7k"}, "exemplar": true, "expr": "tracee_ebpf_network_lostevents_total", "hide": false, "interval": "", "legendFormat": "Network Lost Events", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "w5C9dFs7k"}, "exemplar": true, "expr": "tracee_ebpf_write_lostevents_total", "hide": false, "interval": "", "legendFormat": "Write Lost Events", "refId": "C"}], "title": "Lost Events", "transparent": true, "type": "piechart"}], "refresh": false, "schemaVersion": 35, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "<PERSON><PERSON>", "uid": "6E_JKFy7z", "version": 6, "weekStart": ""}
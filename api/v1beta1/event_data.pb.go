// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.29.0
// source: api/v1beta1/event_data.proto

package v1beta1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SaFamilyT int32

const (
	SaFamilyT_SA_FAMILY_T_UNSPEC SaFamilyT = 0
	// POSIX.1g used the name AF_LOCAL as a synonym for AF_UNIX,
	// but this name is not used in SUSv3.
	SaFamilyT_AF_UNIX  SaFamilyT = 1
	SaFamilyT_AF_INET  SaFamilyT = 2
	SaFamilyT_AF_INET6 SaFamilyT = 10
)

// Enum value maps for SaFamilyT.
var (
	SaFamilyT_name = map[int32]string{
		0:  "SA_FAMILY_T_UNSPEC",
		1:  "AF_UNIX",
		2:  "AF_INET",
		10: "AF_INET6",
	}
	SaFamilyT_value = map[string]int32{
		"SA_FAMILY_T_UNSPEC": 0,
		"AF_UNIX":            1,
		"AF_INET":            2,
		"AF_INET6":           10,
	}
)

func (x SaFamilyT) Enum() *SaFamilyT {
	p := new(SaFamilyT)
	*p = x
	return p
}

func (x SaFamilyT) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SaFamilyT) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1beta1_event_data_proto_enumTypes[0].Descriptor()
}

func (SaFamilyT) Type() protoreflect.EnumType {
	return &file_api_v1beta1_event_data_proto_enumTypes[0]
}

func (x SaFamilyT) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SaFamilyT.Descriptor instead.
func (SaFamilyT) EnumDescriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{0}
}

// https://pkg.go.dev/kernel.org/pub/linux/libs/security/libcap/cap@v1.2.68#Value
type Capability int32

const (
	Capability_CAP_CHOWN              Capability = 0
	Capability_DAC_OVERRIDE           Capability = 1
	Capability_CAP_DAC_READ_SEARCH    Capability = 2
	Capability_CAP_FOWNER             Capability = 3
	Capability_CAP_FSETID             Capability = 4
	Capability_CAP_KILL               Capability = 5
	Capability_CAP_SETGID             Capability = 6
	Capability_CAP_SETUID             Capability = 7
	Capability_CAP_SETPCAP            Capability = 8
	Capability_CAP_NET_BIND_SERVICE   Capability = 10
	Capability_CAP_NET_BROADCAST      Capability = 11
	Capability_CAP_NET_ADMIN          Capability = 12
	Capability_CAP_NET_RAW            Capability = 13
	Capability_CAP_IPC_LOCK           Capability = 14
	Capability_CAP_IPC_OWNER          Capability = 15
	Capability_CAP_SYS_MODULE         Capability = 16
	Capability_CAP_SYS_RAWIO          Capability = 17
	Capability_CAP_SYS_CHROOT         Capability = 18
	Capability_CAP_SYS_PTRACE         Capability = 19
	Capability_CAP_SYS_PACCT          Capability = 20
	Capability_CAP_SYS_ADMIN          Capability = 21
	Capability_CAP_SYS_BOOT           Capability = 22
	Capability_CAP_SYS_NICE           Capability = 23
	Capability_CAP_SYS_RESOURCE       Capability = 24
	Capability_CAP_SYS_TIME           Capability = 25
	Capability_CAP_SYS_TTY_CONFIG     Capability = 26
	Capability_CAP_MKNOD              Capability = 27
	Capability_CAP_LEASE              Capability = 28
	Capability_CAP_AUDIT_WRITE        Capability = 29
	Capability_CAP_AUDIT_CONTROL      Capability = 30
	Capability_CAP_SETFCAP            Capability = 31
	Capability_CAP_MAC_OVERRIDE       Capability = 32
	Capability_CAP_MAC_ADMIN          Capability = 33
	Capability_CAP_SYSLOG             Capability = 34
	Capability_CAP_WAKE_ALARM         Capability = 35
	Capability_CAP_BLOCK_SUSPEND      Capability = 36
	Capability_CAP_AUDIT_READ         Capability = 37
	Capability_CAP_PERFMON            Capability = 38
	Capability_CAP_BPF                Capability = 39
	Capability_CAP_CHECKPOINT_RESTORE Capability = 40
)

// Enum value maps for Capability.
var (
	Capability_name = map[int32]string{
		0:  "CAP_CHOWN",
		1:  "DAC_OVERRIDE",
		2:  "CAP_DAC_READ_SEARCH",
		3:  "CAP_FOWNER",
		4:  "CAP_FSETID",
		5:  "CAP_KILL",
		6:  "CAP_SETGID",
		7:  "CAP_SETUID",
		8:  "CAP_SETPCAP",
		10: "CAP_NET_BIND_SERVICE",
		11: "CAP_NET_BROADCAST",
		12: "CAP_NET_ADMIN",
		13: "CAP_NET_RAW",
		14: "CAP_IPC_LOCK",
		15: "CAP_IPC_OWNER",
		16: "CAP_SYS_MODULE",
		17: "CAP_SYS_RAWIO",
		18: "CAP_SYS_CHROOT",
		19: "CAP_SYS_PTRACE",
		20: "CAP_SYS_PACCT",
		21: "CAP_SYS_ADMIN",
		22: "CAP_SYS_BOOT",
		23: "CAP_SYS_NICE",
		24: "CAP_SYS_RESOURCE",
		25: "CAP_SYS_TIME",
		26: "CAP_SYS_TTY_CONFIG",
		27: "CAP_MKNOD",
		28: "CAP_LEASE",
		29: "CAP_AUDIT_WRITE",
		30: "CAP_AUDIT_CONTROL",
		31: "CAP_SETFCAP",
		32: "CAP_MAC_OVERRIDE",
		33: "CAP_MAC_ADMIN",
		34: "CAP_SYSLOG",
		35: "CAP_WAKE_ALARM",
		36: "CAP_BLOCK_SUSPEND",
		37: "CAP_AUDIT_READ",
		38: "CAP_PERFMON",
		39: "CAP_BPF",
		40: "CAP_CHECKPOINT_RESTORE",
	}
	Capability_value = map[string]int32{
		"CAP_CHOWN":              0,
		"DAC_OVERRIDE":           1,
		"CAP_DAC_READ_SEARCH":    2,
		"CAP_FOWNER":             3,
		"CAP_FSETID":             4,
		"CAP_KILL":               5,
		"CAP_SETGID":             6,
		"CAP_SETUID":             7,
		"CAP_SETPCAP":            8,
		"CAP_NET_BIND_SERVICE":   10,
		"CAP_NET_BROADCAST":      11,
		"CAP_NET_ADMIN":          12,
		"CAP_NET_RAW":            13,
		"CAP_IPC_LOCK":           14,
		"CAP_IPC_OWNER":          15,
		"CAP_SYS_MODULE":         16,
		"CAP_SYS_RAWIO":          17,
		"CAP_SYS_CHROOT":         18,
		"CAP_SYS_PTRACE":         19,
		"CAP_SYS_PACCT":          20,
		"CAP_SYS_ADMIN":          21,
		"CAP_SYS_BOOT":           22,
		"CAP_SYS_NICE":           23,
		"CAP_SYS_RESOURCE":       24,
		"CAP_SYS_TIME":           25,
		"CAP_SYS_TTY_CONFIG":     26,
		"CAP_MKNOD":              27,
		"CAP_LEASE":              28,
		"CAP_AUDIT_WRITE":        29,
		"CAP_AUDIT_CONTROL":      30,
		"CAP_SETFCAP":            31,
		"CAP_MAC_OVERRIDE":       32,
		"CAP_MAC_ADMIN":          33,
		"CAP_SYSLOG":             34,
		"CAP_WAKE_ALARM":         35,
		"CAP_BLOCK_SUSPEND":      36,
		"CAP_AUDIT_READ":         37,
		"CAP_PERFMON":            38,
		"CAP_BPF":                39,
		"CAP_CHECKPOINT_RESTORE": 40,
	}
)

func (x Capability) Enum() *Capability {
	p := new(Capability)
	*p = x
	return p
}

func (x Capability) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Capability) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1beta1_event_data_proto_enumTypes[1].Descriptor()
}

func (Capability) Type() protoreflect.EnumType {
	return &file_api_v1beta1_event_data_proto_enumTypes[1]
}

func (x Capability) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Capability.Descriptor instead.
func (Capability) EnumDescriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{1}
}

type PacketDirection int32

const (
	PacketDirection_INVALID PacketDirection = 0
	PacketDirection_INGRESS PacketDirection = 1
	PacketDirection_EGRESS  PacketDirection = 3
)

// Enum value maps for PacketDirection.
var (
	PacketDirection_name = map[int32]string{
		0: "INVALID",
		1: "INGRESS",
		3: "EGRESS",
	}
	PacketDirection_value = map[string]int32{
		"INVALID": 0,
		"INGRESS": 1,
		"EGRESS":  3,
	}
)

func (x PacketDirection) Enum() *PacketDirection {
	p := new(PacketDirection)
	*p = x
	return p
}

func (x PacketDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PacketDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_api_v1beta1_event_data_proto_enumTypes[2].Descriptor()
}

func (PacketDirection) Type() protoreflect.EnumType {
	return &file_api_v1beta1_event_data_proto_enumTypes[2]
}

func (x PacketDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PacketDirection.Descriptor instead.
func (PacketDirection) EnumDescriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{2}
}

type EventValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Types that are assignable to Value:
	//
	//	*EventValue_Int32
	//	*EventValue_Int64
	//	*EventValue_UInt32
	//	*EventValue_UInt64
	//	*EventValue_Str
	//	*EventValue_Bytes
	//	*EventValue_Bool
	//	*EventValue_StrArray
	//	*EventValue_Int32Array
	//	*EventValue_UInt64Array
	//	*EventValue_Sockaddr
	//	*EventValue_Credentials
	//	*EventValue_Timespec
	//	*EventValue_HookedSyscalls
	//	*EventValue_HookedSeqOps
	//	*EventValue_Ipv4
	//	*EventValue_Ipv6
	//	*EventValue_Tcp
	//	*EventValue_Udp
	//	*EventValue_Icmp
	//	*EventValue_Icmpv6
	//	*EventValue_Dns
	//	*EventValue_DnsQuestions
	//	*EventValue_DnsResponses
	//	*EventValue_PacketMetadata
	//	*EventValue_Http
	//	*EventValue_HttpRequest
	//	*EventValue_HttpResponse
	//	*EventValue_Struct
	Value isEventValue_Value `protobuf_oneof:"value"`
}

func (x *EventValue) Reset() {
	*x = EventValue{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventValue) ProtoMessage() {}

func (x *EventValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventValue.ProtoReflect.Descriptor instead.
func (*EventValue) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{0}
}

func (x *EventValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (m *EventValue) GetValue() isEventValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *EventValue) GetInt32() int32 {
	if x, ok := x.GetValue().(*EventValue_Int32); ok {
		return x.Int32
	}
	return 0
}

func (x *EventValue) GetInt64() int64 {
	if x, ok := x.GetValue().(*EventValue_Int64); ok {
		return x.Int64
	}
	return 0
}

func (x *EventValue) GetUInt32() uint32 {
	if x, ok := x.GetValue().(*EventValue_UInt32); ok {
		return x.UInt32
	}
	return 0
}

func (x *EventValue) GetUInt64() uint64 {
	if x, ok := x.GetValue().(*EventValue_UInt64); ok {
		return x.UInt64
	}
	return 0
}

func (x *EventValue) GetStr() string {
	if x, ok := x.GetValue().(*EventValue_Str); ok {
		return x.Str
	}
	return ""
}

func (x *EventValue) GetBytes() []byte {
	if x, ok := x.GetValue().(*EventValue_Bytes); ok {
		return x.Bytes
	}
	return nil
}

func (x *EventValue) GetBool() bool {
	if x, ok := x.GetValue().(*EventValue_Bool); ok {
		return x.Bool
	}
	return false
}

func (x *EventValue) GetStrArray() *StringArray {
	if x, ok := x.GetValue().(*EventValue_StrArray); ok {
		return x.StrArray
	}
	return nil
}

func (x *EventValue) GetInt32Array() *Int32Array {
	if x, ok := x.GetValue().(*EventValue_Int32Array); ok {
		return x.Int32Array
	}
	return nil
}

func (x *EventValue) GetUInt64Array() *UInt64Array {
	if x, ok := x.GetValue().(*EventValue_UInt64Array); ok {
		return x.UInt64Array
	}
	return nil
}

func (x *EventValue) GetSockaddr() *SockAddr {
	if x, ok := x.GetValue().(*EventValue_Sockaddr); ok {
		return x.Sockaddr
	}
	return nil
}

func (x *EventValue) GetCredentials() *Credentials {
	if x, ok := x.GetValue().(*EventValue_Credentials); ok {
		return x.Credentials
	}
	return nil
}

func (x *EventValue) GetTimespec() *Timespec {
	if x, ok := x.GetValue().(*EventValue_Timespec); ok {
		return x.Timespec
	}
	return nil
}

func (x *EventValue) GetHookedSyscalls() *HookedSyscalls {
	if x, ok := x.GetValue().(*EventValue_HookedSyscalls); ok {
		return x.HookedSyscalls
	}
	return nil
}

func (x *EventValue) GetHookedSeqOps() *HookedSeqOps {
	if x, ok := x.GetValue().(*EventValue_HookedSeqOps); ok {
		return x.HookedSeqOps
	}
	return nil
}

func (x *EventValue) GetIpv4() *IPv4 {
	if x, ok := x.GetValue().(*EventValue_Ipv4); ok {
		return x.Ipv4
	}
	return nil
}

func (x *EventValue) GetIpv6() *IPv6 {
	if x, ok := x.GetValue().(*EventValue_Ipv6); ok {
		return x.Ipv6
	}
	return nil
}

func (x *EventValue) GetTcp() *TCP {
	if x, ok := x.GetValue().(*EventValue_Tcp); ok {
		return x.Tcp
	}
	return nil
}

func (x *EventValue) GetUdp() *UDP {
	if x, ok := x.GetValue().(*EventValue_Udp); ok {
		return x.Udp
	}
	return nil
}

func (x *EventValue) GetIcmp() *ICMP {
	if x, ok := x.GetValue().(*EventValue_Icmp); ok {
		return x.Icmp
	}
	return nil
}

func (x *EventValue) GetIcmpv6() *ICMPv6 {
	if x, ok := x.GetValue().(*EventValue_Icmpv6); ok {
		return x.Icmpv6
	}
	return nil
}

func (x *EventValue) GetDns() *DNS {
	if x, ok := x.GetValue().(*EventValue_Dns); ok {
		return x.Dns
	}
	return nil
}

func (x *EventValue) GetDnsQuestions() *DnsQuestions {
	if x, ok := x.GetValue().(*EventValue_DnsQuestions); ok {
		return x.DnsQuestions
	}
	return nil
}

func (x *EventValue) GetDnsResponses() *DnsResponses {
	if x, ok := x.GetValue().(*EventValue_DnsResponses); ok {
		return x.DnsResponses
	}
	return nil
}

func (x *EventValue) GetPacketMetadata() *PacketMetadata {
	if x, ok := x.GetValue().(*EventValue_PacketMetadata); ok {
		return x.PacketMetadata
	}
	return nil
}

func (x *EventValue) GetHttp() *HTTP {
	if x, ok := x.GetValue().(*EventValue_Http); ok {
		return x.Http
	}
	return nil
}

func (x *EventValue) GetHttpRequest() *HTTPRequest {
	if x, ok := x.GetValue().(*EventValue_HttpRequest); ok {
		return x.HttpRequest
	}
	return nil
}

func (x *EventValue) GetHttpResponse() *HTTPResponse {
	if x, ok := x.GetValue().(*EventValue_HttpResponse); ok {
		return x.HttpResponse
	}
	return nil
}

func (x *EventValue) GetStruct() *structpb.Struct {
	if x, ok := x.GetValue().(*EventValue_Struct); ok {
		return x.Struct
	}
	return nil
}

type isEventValue_Value interface {
	isEventValue_Value()
}

type EventValue_Int32 struct {
	Int32 int32 `protobuf:"varint,2,opt,name=int32,proto3,oneof"` // intT
}

type EventValue_Int64 struct {
	Int64 int64 `protobuf:"varint,3,opt,name=int64,proto3,oneof"` // longT
}

type EventValue_UInt32 struct {
	UInt32 uint32 `protobuf:"varint,4,opt,name=u_int32,json=uInt32,proto3,oneof"` // uintT, modeT, devT, u8T, u16T
}

type EventValue_UInt64 struct {
	UInt64 uint64 `protobuf:"varint,5,opt,name=u_int64,json=uInt64,proto3,oneof"` // ulongT, offT, sizeT, pointerT
}

type EventValue_Str struct {
	Str string `protobuf:"bytes,6,opt,name=str,proto3,oneof"` // strT
}

type EventValue_Bytes struct {
	Bytes []byte `protobuf:"bytes,7,opt,name=bytes,proto3,oneof"` // bytesT
}

type EventValue_Bool struct {
	Bool bool `protobuf:"varint,8,opt,name=bool,proto3,oneof"` // boolT
}

type EventValue_StrArray struct {
	StrArray *StringArray `protobuf:"bytes,9,opt,name=str_array,json=strArray,proto3,oneof"` // strArrT, argsArrT
}

type EventValue_Int32Array struct {
	Int32Array *Int32Array `protobuf:"bytes,10,opt,name=int32_array,json=int32Array,proto3,oneof"` // intArr2T
}

type EventValue_UInt64Array struct {
	UInt64Array *UInt64Array `protobuf:"bytes,11,opt,name=u_int64_array,json=uInt64Array,proto3,oneof"` // uint64ArrT
}

type EventValue_Sockaddr struct {
	Sockaddr *SockAddr `protobuf:"bytes,12,opt,name=sockaddr,proto3,oneof"` // sockaddrT
}

type EventValue_Credentials struct {
	Credentials *Credentials `protobuf:"bytes,13,opt,name=credentials,proto3,oneof"` // credT
}

type EventValue_Timespec struct {
	Timespec *Timespec `protobuf:"bytes,14,opt,name=timespec,proto3,oneof"` // timespecT
}

type EventValue_HookedSyscalls struct {
	HookedSyscalls *HookedSyscalls `protobuf:"bytes,15,opt,name=hooked_syscalls,json=hookedSyscalls,proto3,oneof"`
}

type EventValue_HookedSeqOps struct {
	HookedSeqOps *HookedSeqOps `protobuf:"bytes,16,opt,name=hooked_seq_ops,json=hookedSeqOps,proto3,oneof"`
}

type EventValue_Ipv4 struct {
	Ipv4 *IPv4 `protobuf:"bytes,17,opt,name=ipv4,proto3,oneof"`
}

type EventValue_Ipv6 struct {
	Ipv6 *IPv6 `protobuf:"bytes,18,opt,name=ipv6,proto3,oneof"`
}

type EventValue_Tcp struct {
	Tcp *TCP `protobuf:"bytes,19,opt,name=tcp,proto3,oneof"`
}

type EventValue_Udp struct {
	Udp *UDP `protobuf:"bytes,20,opt,name=udp,proto3,oneof"`
}

type EventValue_Icmp struct {
	Icmp *ICMP `protobuf:"bytes,21,opt,name=icmp,proto3,oneof"`
}

type EventValue_Icmpv6 struct {
	Icmpv6 *ICMPv6 `protobuf:"bytes,22,opt,name=icmpv6,proto3,oneof"`
}

type EventValue_Dns struct {
	Dns *DNS `protobuf:"bytes,23,opt,name=dns,proto3,oneof"`
}

type EventValue_DnsQuestions struct {
	DnsQuestions *DnsQuestions `protobuf:"bytes,24,opt,name=dns_questions,json=dnsQuestions,proto3,oneof"`
}

type EventValue_DnsResponses struct {
	DnsResponses *DnsResponses `protobuf:"bytes,25,opt,name=dns_responses,json=dnsResponses,proto3,oneof"`
}

type EventValue_PacketMetadata struct {
	PacketMetadata *PacketMetadata `protobuf:"bytes,26,opt,name=packet_metadata,json=packetMetadata,proto3,oneof"`
}

type EventValue_Http struct {
	Http *HTTP `protobuf:"bytes,27,opt,name=http,proto3,oneof"`
}

type EventValue_HttpRequest struct {
	HttpRequest *HTTPRequest `protobuf:"bytes,28,opt,name=http_request,json=httpRequest,proto3,oneof"`
}

type EventValue_HttpResponse struct {
	HttpResponse *HTTPResponse `protobuf:"bytes,29,opt,name=http_response,json=httpResponse,proto3,oneof"`
}

type EventValue_Struct struct {
	Struct *structpb.Struct `protobuf:"bytes,30,opt,name=struct,proto3,oneof"`
}

func (*EventValue_Int32) isEventValue_Value() {}

func (*EventValue_Int64) isEventValue_Value() {}

func (*EventValue_UInt32) isEventValue_Value() {}

func (*EventValue_UInt64) isEventValue_Value() {}

func (*EventValue_Str) isEventValue_Value() {}

func (*EventValue_Bytes) isEventValue_Value() {}

func (*EventValue_Bool) isEventValue_Value() {}

func (*EventValue_StrArray) isEventValue_Value() {}

func (*EventValue_Int32Array) isEventValue_Value() {}

func (*EventValue_UInt64Array) isEventValue_Value() {}

func (*EventValue_Sockaddr) isEventValue_Value() {}

func (*EventValue_Credentials) isEventValue_Value() {}

func (*EventValue_Timespec) isEventValue_Value() {}

func (*EventValue_HookedSyscalls) isEventValue_Value() {}

func (*EventValue_HookedSeqOps) isEventValue_Value() {}

func (*EventValue_Ipv4) isEventValue_Value() {}

func (*EventValue_Ipv6) isEventValue_Value() {}

func (*EventValue_Tcp) isEventValue_Value() {}

func (*EventValue_Udp) isEventValue_Value() {}

func (*EventValue_Icmp) isEventValue_Value() {}

func (*EventValue_Icmpv6) isEventValue_Value() {}

func (*EventValue_Dns) isEventValue_Value() {}

func (*EventValue_DnsQuestions) isEventValue_Value() {}

func (*EventValue_DnsResponses) isEventValue_Value() {}

func (*EventValue_PacketMetadata) isEventValue_Value() {}

func (*EventValue_Http) isEventValue_Value() {}

func (*EventValue_HttpRequest) isEventValue_Value() {}

func (*EventValue_HttpResponse) isEventValue_Value() {}

func (*EventValue_Struct) isEventValue_Value() {}

type StringArray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *StringArray) Reset() {
	*x = StringArray{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringArray) ProtoMessage() {}

func (x *StringArray) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringArray.ProtoReflect.Descriptor instead.
func (*StringArray) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{1}
}

func (x *StringArray) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

type Int32Array struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []int32 `protobuf:"varint,1,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *Int32Array) Reset() {
	*x = Int32Array{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Int32Array) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32Array) ProtoMessage() {}

func (x *Int32Array) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32Array.ProtoReflect.Descriptor instead.
func (*Int32Array) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{2}
}

func (x *Int32Array) GetValue() []int32 {
	if x != nil {
		return x.Value
	}
	return nil
}

type UInt64Array struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []uint64 `protobuf:"varint,1,rep,packed,name=value,proto3" json:"value,omitempty"`
}

func (x *UInt64Array) Reset() {
	*x = UInt64Array{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UInt64Array) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UInt64Array) ProtoMessage() {}

func (x *UInt64Array) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UInt64Array.ProtoReflect.Descriptor instead.
func (*UInt64Array) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{3}
}

func (x *UInt64Array) GetValue() []uint64 {
	if x != nil {
		return x.Value
	}
	return nil
}

type Credentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid            *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Gid            *wrapperspb.UInt32Value `protobuf:"bytes,2,opt,name=gid,proto3" json:"gid,omitempty"`
	Suid           *wrapperspb.UInt32Value `protobuf:"bytes,3,opt,name=suid,proto3" json:"suid,omitempty"`
	Sgid           *wrapperspb.UInt32Value `protobuf:"bytes,4,opt,name=sgid,proto3" json:"sgid,omitempty"`
	Euid           *wrapperspb.UInt32Value `protobuf:"bytes,5,opt,name=euid,proto3" json:"euid,omitempty"`
	Egid           *wrapperspb.UInt32Value `protobuf:"bytes,6,opt,name=egid,proto3" json:"egid,omitempty"`
	Fsuid          *wrapperspb.UInt32Value `protobuf:"bytes,7,opt,name=fsuid,proto3" json:"fsuid,omitempty"`
	Fsgid          *wrapperspb.UInt32Value `protobuf:"bytes,8,opt,name=fsgid,proto3" json:"fsgid,omitempty"`
	UserNamespace  *wrapperspb.UInt32Value `protobuf:"bytes,9,opt,name=user_namespace,json=userNamespace,proto3" json:"user_namespace,omitempty"`
	SecureBits     *wrapperspb.UInt32Value `protobuf:"bytes,10,opt,name=secure_bits,json=secureBits,proto3" json:"secure_bits,omitempty"`
	CapInheritable []Capability            `protobuf:"varint,11,rep,packed,name=cap_inheritable,json=capInheritable,proto3,enum=tracee.v1beta1.Capability" json:"cap_inheritable,omitempty"`
	CapPermitted   []Capability            `protobuf:"varint,12,rep,packed,name=cap_permitted,json=capPermitted,proto3,enum=tracee.v1beta1.Capability" json:"cap_permitted,omitempty"`
	CapEffective   []Capability            `protobuf:"varint,13,rep,packed,name=cap_effective,json=capEffective,proto3,enum=tracee.v1beta1.Capability" json:"cap_effective,omitempty"`
	CapBounding    []Capability            `protobuf:"varint,14,rep,packed,name=cap_bounding,json=capBounding,proto3,enum=tracee.v1beta1.Capability" json:"cap_bounding,omitempty"`
	CapAmbient     []Capability            `protobuf:"varint,15,rep,packed,name=cap_ambient,json=capAmbient,proto3,enum=tracee.v1beta1.Capability" json:"cap_ambient,omitempty"`
}

func (x *Credentials) Reset() {
	*x = Credentials{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Credentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Credentials) ProtoMessage() {}

func (x *Credentials) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Credentials.ProtoReflect.Descriptor instead.
func (*Credentials) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{4}
}

func (x *Credentials) GetUid() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Uid
	}
	return nil
}

func (x *Credentials) GetGid() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Gid
	}
	return nil
}

func (x *Credentials) GetSuid() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Suid
	}
	return nil
}

func (x *Credentials) GetSgid() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Sgid
	}
	return nil
}

func (x *Credentials) GetEuid() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Euid
	}
	return nil
}

func (x *Credentials) GetEgid() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Egid
	}
	return nil
}

func (x *Credentials) GetFsuid() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Fsuid
	}
	return nil
}

func (x *Credentials) GetFsgid() *wrapperspb.UInt32Value {
	if x != nil {
		return x.Fsgid
	}
	return nil
}

func (x *Credentials) GetUserNamespace() *wrapperspb.UInt32Value {
	if x != nil {
		return x.UserNamespace
	}
	return nil
}

func (x *Credentials) GetSecureBits() *wrapperspb.UInt32Value {
	if x != nil {
		return x.SecureBits
	}
	return nil
}

func (x *Credentials) GetCapInheritable() []Capability {
	if x != nil {
		return x.CapInheritable
	}
	return nil
}

func (x *Credentials) GetCapPermitted() []Capability {
	if x != nil {
		return x.CapPermitted
	}
	return nil
}

func (x *Credentials) GetCapEffective() []Capability {
	if x != nil {
		return x.CapEffective
	}
	return nil
}

func (x *Credentials) GetCapBounding() []Capability {
	if x != nil {
		return x.CapBounding
	}
	return nil
}

func (x *Credentials) GetCapAmbient() []Capability {
	if x != nil {
		return x.CapAmbient
	}
	return nil
}

type Timespec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value *wrapperspb.DoubleValue `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Timespec) Reset() {
	*x = Timespec{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Timespec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Timespec) ProtoMessage() {}

func (x *Timespec) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Timespec.ProtoReflect.Descriptor instead.
func (*Timespec) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{5}
}

func (x *Timespec) GetValue() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Value
	}
	return nil
}

type SockAddr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SaFamily     SaFamilyT `protobuf:"varint,1,opt,name=sa_family,json=saFamily,proto3,enum=tracee.v1beta1.SaFamilyT" json:"sa_family,omitempty"`
	SunPath      string    `protobuf:"bytes,2,opt,name=sun_path,json=sunPath,proto3" json:"sun_path,omitempty"`
	SinAddr      string    `protobuf:"bytes,3,opt,name=sin_addr,json=sinAddr,proto3" json:"sin_addr,omitempty"`
	SinPort      uint32    `protobuf:"varint,4,opt,name=sin_port,json=sinPort,proto3" json:"sin_port,omitempty"`
	Sin6Addr     string    `protobuf:"bytes,5,opt,name=sin6_addr,json=sin6Addr,proto3" json:"sin6_addr,omitempty"`
	Sin6Port     uint32    `protobuf:"varint,6,opt,name=sin6_port,json=sin6Port,proto3" json:"sin6_port,omitempty"`
	Sin6Flowinfo uint32    `protobuf:"varint,7,opt,name=sin6_flowinfo,json=sin6Flowinfo,proto3" json:"sin6_flowinfo,omitempty"`
	Sin6Scopeid  uint32    `protobuf:"varint,8,opt,name=sin6_scopeid,json=sin6Scopeid,proto3" json:"sin6_scopeid,omitempty"`
}

func (x *SockAddr) Reset() {
	*x = SockAddr{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SockAddr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SockAddr) ProtoMessage() {}

func (x *SockAddr) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SockAddr.ProtoReflect.Descriptor instead.
func (*SockAddr) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{6}
}

func (x *SockAddr) GetSaFamily() SaFamilyT {
	if x != nil {
		return x.SaFamily
	}
	return SaFamilyT_SA_FAMILY_T_UNSPEC
}

func (x *SockAddr) GetSunPath() string {
	if x != nil {
		return x.SunPath
	}
	return ""
}

func (x *SockAddr) GetSinAddr() string {
	if x != nil {
		return x.SinAddr
	}
	return ""
}

func (x *SockAddr) GetSinPort() uint32 {
	if x != nil {
		return x.SinPort
	}
	return 0
}

func (x *SockAddr) GetSin6Addr() string {
	if x != nil {
		return x.Sin6Addr
	}
	return ""
}

func (x *SockAddr) GetSin6Port() uint32 {
	if x != nil {
		return x.Sin6Port
	}
	return 0
}

func (x *SockAddr) GetSin6Flowinfo() uint32 {
	if x != nil {
		return x.Sin6Flowinfo
	}
	return 0
}

func (x *SockAddr) GetSin6Scopeid() uint32 {
	if x != nil {
		return x.Sin6Scopeid
	}
	return 0
}

type HookedSyscalls struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value []*HookedSymbolData `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *HookedSyscalls) Reset() {
	*x = HookedSyscalls{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HookedSyscalls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookedSyscalls) ProtoMessage() {}

func (x *HookedSyscalls) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookedSyscalls.ProtoReflect.Descriptor instead.
func (*HookedSyscalls) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{7}
}

func (x *HookedSyscalls) GetValue() []*HookedSymbolData {
	if x != nil {
		return x.Value
	}
	return nil
}

type HookedSymbolData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SymbolName  string `protobuf:"bytes,1,opt,name=symbol_name,json=symbolName,proto3" json:"symbol_name,omitempty"`
	ModuleOwner string `protobuf:"bytes,2,opt,name=module_owner,json=moduleOwner,proto3" json:"module_owner,omitempty"`
}

func (x *HookedSymbolData) Reset() {
	*x = HookedSymbolData{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HookedSymbolData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookedSymbolData) ProtoMessage() {}

func (x *HookedSymbolData) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookedSymbolData.ProtoReflect.Descriptor instead.
func (*HookedSymbolData) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{8}
}

func (x *HookedSymbolData) GetSymbolName() string {
	if x != nil {
		return x.SymbolName
	}
	return ""
}

func (x *HookedSymbolData) GetModuleOwner() string {
	if x != nil {
		return x.ModuleOwner
	}
	return ""
}

type HookedSeqOps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value map[string]*HookedSymbolData `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *HookedSeqOps) Reset() {
	*x = HookedSeqOps{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HookedSeqOps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookedSeqOps) ProtoMessage() {}

func (x *HookedSeqOps) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookedSeqOps.ProtoReflect.Descriptor instead.
func (*HookedSeqOps) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{9}
}

func (x *HookedSeqOps) GetValue() map[string]*HookedSymbolData {
	if x != nil {
		return x.Value
	}
	return nil
}

type IPv4 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version    uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Ihl        uint32 `protobuf:"varint,2,opt,name=ihl,proto3" json:"ihl,omitempty"`
	Tos        uint32 `protobuf:"varint,3,opt,name=tos,proto3" json:"tos,omitempty"`
	Length     uint32 `protobuf:"varint,4,opt,name=length,proto3" json:"length,omitempty"`
	Id         uint32 `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`
	Flags      uint32 `protobuf:"varint,6,opt,name=flags,proto3" json:"flags,omitempty"`
	FragOffset uint32 `protobuf:"varint,7,opt,name=frag_offset,json=fragOffset,proto3" json:"frag_offset,omitempty"`
	Ttl        uint32 `protobuf:"varint,8,opt,name=ttl,proto3" json:"ttl,omitempty"`
	Protocol   string `protobuf:"bytes,9,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Checksum   uint32 `protobuf:"varint,10,opt,name=checksum,proto3" json:"checksum,omitempty"`
	SrcIp      string `protobuf:"bytes,11,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	DstIp      string `protobuf:"bytes,12,opt,name=dst_ip,json=dstIp,proto3" json:"dst_ip,omitempty"`
}

func (x *IPv4) Reset() {
	*x = IPv4{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPv4) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPv4) ProtoMessage() {}

func (x *IPv4) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPv4.ProtoReflect.Descriptor instead.
func (*IPv4) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{10}
}

func (x *IPv4) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *IPv4) GetIhl() uint32 {
	if x != nil {
		return x.Ihl
	}
	return 0
}

func (x *IPv4) GetTos() uint32 {
	if x != nil {
		return x.Tos
	}
	return 0
}

func (x *IPv4) GetLength() uint32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *IPv4) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *IPv4) GetFlags() uint32 {
	if x != nil {
		return x.Flags
	}
	return 0
}

func (x *IPv4) GetFragOffset() uint32 {
	if x != nil {
		return x.FragOffset
	}
	return 0
}

func (x *IPv4) GetTtl() uint32 {
	if x != nil {
		return x.Ttl
	}
	return 0
}

func (x *IPv4) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *IPv4) GetChecksum() uint32 {
	if x != nil {
		return x.Checksum
	}
	return 0
}

func (x *IPv4) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *IPv4) GetDstIp() string {
	if x != nil {
		return x.DstIp
	}
	return ""
}

type IPv6 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version      uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	TrafficClass uint32 `protobuf:"varint,2,opt,name=traffic_class,json=trafficClass,proto3" json:"traffic_class,omitempty"`
	FlowLabel    uint32 `protobuf:"varint,3,opt,name=flow_label,json=flowLabel,proto3" json:"flow_label,omitempty"`
	Length       uint32 `protobuf:"varint,4,opt,name=length,proto3" json:"length,omitempty"`
	NextHeader   string `protobuf:"bytes,5,opt,name=next_header,json=nextHeader,proto3" json:"next_header,omitempty"`
	HopLimit     uint32 `protobuf:"varint,6,opt,name=hop_limit,json=hopLimit,proto3" json:"hop_limit,omitempty"`
	SrcIp        string `protobuf:"bytes,7,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	DstIp        string `protobuf:"bytes,8,opt,name=dst_ip,json=dstIp,proto3" json:"dst_ip,omitempty"`
}

func (x *IPv6) Reset() {
	*x = IPv6{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPv6) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPv6) ProtoMessage() {}

func (x *IPv6) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPv6.ProtoReflect.Descriptor instead.
func (*IPv6) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{11}
}

func (x *IPv6) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *IPv6) GetTrafficClass() uint32 {
	if x != nil {
		return x.TrafficClass
	}
	return 0
}

func (x *IPv6) GetFlowLabel() uint32 {
	if x != nil {
		return x.FlowLabel
	}
	return 0
}

func (x *IPv6) GetLength() uint32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *IPv6) GetNextHeader() string {
	if x != nil {
		return x.NextHeader
	}
	return ""
}

func (x *IPv6) GetHopLimit() uint32 {
	if x != nil {
		return x.HopLimit
	}
	return 0
}

func (x *IPv6) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *IPv6) GetDstIp() string {
	if x != nil {
		return x.DstIp
	}
	return ""
}

type TCP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrcPort    uint32 `protobuf:"varint,1,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`
	DstPort    uint32 `protobuf:"varint,2,opt,name=dst_port,json=dstPort,proto3" json:"dst_port,omitempty"`
	Seq        uint32 `protobuf:"varint,3,opt,name=seq,proto3" json:"seq,omitempty"`
	Ack        uint32 `protobuf:"varint,4,opt,name=ack,proto3" json:"ack,omitempty"`
	DataOffset uint32 `protobuf:"varint,5,opt,name=data_offset,json=dataOffset,proto3" json:"data_offset,omitempty"`
	FinFlag    uint32 `protobuf:"varint,6,opt,name=fin_flag,json=finFlag,proto3" json:"fin_flag,omitempty"`
	SynFlag    uint32 `protobuf:"varint,7,opt,name=syn_flag,json=synFlag,proto3" json:"syn_flag,omitempty"`
	RstFlag    uint32 `protobuf:"varint,8,opt,name=rst_flag,json=rstFlag,proto3" json:"rst_flag,omitempty"`
	PshFlag    uint32 `protobuf:"varint,9,opt,name=psh_flag,json=pshFlag,proto3" json:"psh_flag,omitempty"`
	AckFlag    uint32 `protobuf:"varint,10,opt,name=ack_flag,json=ackFlag,proto3" json:"ack_flag,omitempty"`
	UrgFlag    uint32 `protobuf:"varint,11,opt,name=urg_flag,json=urgFlag,proto3" json:"urg_flag,omitempty"`
	EceFlag    uint32 `protobuf:"varint,12,opt,name=ece_flag,json=eceFlag,proto3" json:"ece_flag,omitempty"`
	CwrFlag    uint32 `protobuf:"varint,13,opt,name=cwr_flag,json=cwrFlag,proto3" json:"cwr_flag,omitempty"`
	NsFlag     uint32 `protobuf:"varint,14,opt,name=ns_flag,json=nsFlag,proto3" json:"ns_flag,omitempty"`
	Window     uint32 `protobuf:"varint,15,opt,name=window,proto3" json:"window,omitempty"`
	Checksum   uint32 `protobuf:"varint,16,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Urgent     uint32 `protobuf:"varint,17,opt,name=urgent,proto3" json:"urgent,omitempty"`
}

func (x *TCP) Reset() {
	*x = TCP{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCP) ProtoMessage() {}

func (x *TCP) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCP.ProtoReflect.Descriptor instead.
func (*TCP) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{12}
}

func (x *TCP) GetSrcPort() uint32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *TCP) GetDstPort() uint32 {
	if x != nil {
		return x.DstPort
	}
	return 0
}

func (x *TCP) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *TCP) GetAck() uint32 {
	if x != nil {
		return x.Ack
	}
	return 0
}

func (x *TCP) GetDataOffset() uint32 {
	if x != nil {
		return x.DataOffset
	}
	return 0
}

func (x *TCP) GetFinFlag() uint32 {
	if x != nil {
		return x.FinFlag
	}
	return 0
}

func (x *TCP) GetSynFlag() uint32 {
	if x != nil {
		return x.SynFlag
	}
	return 0
}

func (x *TCP) GetRstFlag() uint32 {
	if x != nil {
		return x.RstFlag
	}
	return 0
}

func (x *TCP) GetPshFlag() uint32 {
	if x != nil {
		return x.PshFlag
	}
	return 0
}

func (x *TCP) GetAckFlag() uint32 {
	if x != nil {
		return x.AckFlag
	}
	return 0
}

func (x *TCP) GetUrgFlag() uint32 {
	if x != nil {
		return x.UrgFlag
	}
	return 0
}

func (x *TCP) GetEceFlag() uint32 {
	if x != nil {
		return x.EceFlag
	}
	return 0
}

func (x *TCP) GetCwrFlag() uint32 {
	if x != nil {
		return x.CwrFlag
	}
	return 0
}

func (x *TCP) GetNsFlag() uint32 {
	if x != nil {
		return x.NsFlag
	}
	return 0
}

func (x *TCP) GetWindow() uint32 {
	if x != nil {
		return x.Window
	}
	return 0
}

func (x *TCP) GetChecksum() uint32 {
	if x != nil {
		return x.Checksum
	}
	return 0
}

func (x *TCP) GetUrgent() uint32 {
	if x != nil {
		return x.Urgent
	}
	return 0
}

type UDP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrcPort  uint32 `protobuf:"varint,1,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`
	DstPort  uint32 `protobuf:"varint,2,opt,name=dst_port,json=dstPort,proto3" json:"dst_port,omitempty"`
	Length   uint32 `protobuf:"varint,3,opt,name=length,proto3" json:"length,omitempty"`
	Checksum uint32 `protobuf:"varint,4,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *UDP) Reset() {
	*x = UDP{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UDP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UDP) ProtoMessage() {}

func (x *UDP) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UDP.ProtoReflect.Descriptor instead.
func (*UDP) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{13}
}

func (x *UDP) GetSrcPort() uint32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *UDP) GetDstPort() uint32 {
	if x != nil {
		return x.DstPort
	}
	return 0
}

func (x *UDP) GetLength() uint32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *UDP) GetChecksum() uint32 {
	if x != nil {
		return x.Checksum
	}
	return 0
}

type ICMP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeCode string `protobuf:"bytes,1,opt,name=type_code,json=typeCode,proto3" json:"type_code,omitempty"`
	Checksum uint32 `protobuf:"varint,2,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Id       uint32 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	Seq      uint32 `protobuf:"varint,4,opt,name=seq,proto3" json:"seq,omitempty"`
}

func (x *ICMP) Reset() {
	*x = ICMP{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ICMP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ICMP) ProtoMessage() {}

func (x *ICMP) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ICMP.ProtoReflect.Descriptor instead.
func (*ICMP) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{14}
}

func (x *ICMP) GetTypeCode() string {
	if x != nil {
		return x.TypeCode
	}
	return ""
}

func (x *ICMP) GetChecksum() uint32 {
	if x != nil {
		return x.Checksum
	}
	return 0
}

func (x *ICMP) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ICMP) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type ICMPv6 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeCode string `protobuf:"bytes,1,opt,name=type_code,json=typeCode,proto3" json:"type_code,omitempty"`
	Checksum uint32 `protobuf:"varint,2,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (x *ICMPv6) Reset() {
	*x = ICMPv6{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ICMPv6) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ICMPv6) ProtoMessage() {}

func (x *ICMPv6) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ICMPv6.ProtoReflect.Descriptor instead.
func (*ICMPv6) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{15}
}

func (x *ICMPv6) GetTypeCode() string {
	if x != nil {
		return x.TypeCode
	}
	return ""
}

func (x *ICMPv6) GetChecksum() uint32 {
	if x != nil {
		return x.Checksum
	}
	return 0
}

type DnsQuestions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Questions []*DnsQueryData `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
}

func (x *DnsQuestions) Reset() {
	*x = DnsQuestions{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsQuestions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsQuestions) ProtoMessage() {}

func (x *DnsQuestions) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsQuestions.ProtoReflect.Descriptor instead.
func (*DnsQuestions) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{16}
}

func (x *DnsQuestions) GetQuestions() []*DnsQueryData {
	if x != nil {
		return x.Questions
	}
	return nil
}

type DnsResponses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Responses []*DnsResponseData `protobuf:"bytes,1,rep,name=responses,proto3" json:"responses,omitempty"`
}

func (x *DnsResponses) Reset() {
	*x = DnsResponses{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsResponses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsResponses) ProtoMessage() {}

func (x *DnsResponses) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsResponses.ProtoReflect.Descriptor instead.
func (*DnsResponses) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{17}
}

func (x *DnsResponses) GetResponses() []*DnsResponseData {
	if x != nil {
		return x.Responses
	}
	return nil
}

type DNS struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Qr           uint32               `protobuf:"varint,2,opt,name=qr,proto3" json:"qr,omitempty"`
	OpCode       string               `protobuf:"bytes,3,opt,name=op_code,json=opCode,proto3" json:"op_code,omitempty"`
	Aa           uint32               `protobuf:"varint,4,opt,name=aa,proto3" json:"aa,omitempty"`
	Tc           uint32               `protobuf:"varint,5,opt,name=tc,proto3" json:"tc,omitempty"`
	Rd           uint32               `protobuf:"varint,6,opt,name=rd,proto3" json:"rd,omitempty"`
	Ra           uint32               `protobuf:"varint,7,opt,name=ra,proto3" json:"ra,omitempty"`
	Z            uint32               `protobuf:"varint,8,opt,name=z,proto3" json:"z,omitempty"`
	ResponseCode string               `protobuf:"bytes,9,opt,name=response_code,json=responseCode,proto3" json:"response_code,omitempty"`
	QdCount      uint32               `protobuf:"varint,10,opt,name=qd_count,json=qdCount,proto3" json:"qd_count,omitempty"`
	AnCount      uint32               `protobuf:"varint,11,opt,name=an_count,json=anCount,proto3" json:"an_count,omitempty"`
	NsCount      uint32               `protobuf:"varint,12,opt,name=ns_count,json=nsCount,proto3" json:"ns_count,omitempty"`
	ArCount      uint32               `protobuf:"varint,13,opt,name=ar_count,json=arCount,proto3" json:"ar_count,omitempty"`
	Questions    []*DNSQuestion       `protobuf:"bytes,14,rep,name=questions,proto3" json:"questions,omitempty"`
	Answers      []*DNSResourceRecord `protobuf:"bytes,15,rep,name=answers,proto3" json:"answers,omitempty"`
	Authorities  []*DNSResourceRecord `protobuf:"bytes,16,rep,name=authorities,proto3" json:"authorities,omitempty"`
	Additionals  []*DNSResourceRecord `protobuf:"bytes,17,rep,name=additionals,proto3" json:"additionals,omitempty"`
}

func (x *DNS) Reset() {
	*x = DNS{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DNS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNS) ProtoMessage() {}

func (x *DNS) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNS.ProtoReflect.Descriptor instead.
func (*DNS) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{18}
}

func (x *DNS) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DNS) GetQr() uint32 {
	if x != nil {
		return x.Qr
	}
	return 0
}

func (x *DNS) GetOpCode() string {
	if x != nil {
		return x.OpCode
	}
	return ""
}

func (x *DNS) GetAa() uint32 {
	if x != nil {
		return x.Aa
	}
	return 0
}

func (x *DNS) GetTc() uint32 {
	if x != nil {
		return x.Tc
	}
	return 0
}

func (x *DNS) GetRd() uint32 {
	if x != nil {
		return x.Rd
	}
	return 0
}

func (x *DNS) GetRa() uint32 {
	if x != nil {
		return x.Ra
	}
	return 0
}

func (x *DNS) GetZ() uint32 {
	if x != nil {
		return x.Z
	}
	return 0
}

func (x *DNS) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *DNS) GetQdCount() uint32 {
	if x != nil {
		return x.QdCount
	}
	return 0
}

func (x *DNS) GetAnCount() uint32 {
	if x != nil {
		return x.AnCount
	}
	return 0
}

func (x *DNS) GetNsCount() uint32 {
	if x != nil {
		return x.NsCount
	}
	return 0
}

func (x *DNS) GetArCount() uint32 {
	if x != nil {
		return x.ArCount
	}
	return 0
}

func (x *DNS) GetQuestions() []*DNSQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

func (x *DNS) GetAnswers() []*DNSResourceRecord {
	if x != nil {
		return x.Answers
	}
	return nil
}

func (x *DNS) GetAuthorities() []*DNSResourceRecord {
	if x != nil {
		return x.Authorities
	}
	return nil
}

func (x *DNS) GetAdditionals() []*DNSResourceRecord {
	if x != nil {
		return x.Additionals
	}
	return nil
}

type DNSQuestion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type  string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Class string `protobuf:"bytes,3,opt,name=class,proto3" json:"class,omitempty"`
}

func (x *DNSQuestion) Reset() {
	*x = DNSQuestion{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DNSQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSQuestion) ProtoMessage() {}

func (x *DNSQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSQuestion.ProtoReflect.Descriptor instead.
func (*DNSQuestion) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{19}
}

func (x *DNSQuestion) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DNSQuestion) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DNSQuestion) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

type DNSResourceRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type  string    `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Class string    `protobuf:"bytes,3,opt,name=class,proto3" json:"class,omitempty"`
	Ttl   uint32    `protobuf:"varint,4,opt,name=ttl,proto3" json:"ttl,omitempty"`
	Ip    string    `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	Ns    string    `protobuf:"bytes,6,opt,name=ns,proto3" json:"ns,omitempty"`
	Cname string    `protobuf:"bytes,7,opt,name=cname,proto3" json:"cname,omitempty"`
	Ptr   string    `protobuf:"bytes,8,opt,name=ptr,proto3" json:"ptr,omitempty"`
	Txts  []string  `protobuf:"bytes,9,rep,name=txts,proto3" json:"txts,omitempty"`
	Soa   *DNSSOA   `protobuf:"bytes,10,opt,name=soa,proto3" json:"soa,omitempty"`
	Srv   *DNSSRV   `protobuf:"bytes,11,opt,name=srv,proto3" json:"srv,omitempty"`
	Mx    *DNSMX    `protobuf:"bytes,12,opt,name=mx,proto3" json:"mx,omitempty"`
	Opt   []*DNSOPT `protobuf:"bytes,13,rep,name=opt,proto3" json:"opt,omitempty"`
	Uri   *DNSURI   `protobuf:"bytes,14,opt,name=uri,proto3" json:"uri,omitempty"`
	Txt   string    `protobuf:"bytes,15,opt,name=txt,proto3" json:"txt,omitempty"`
}

func (x *DNSResourceRecord) Reset() {
	*x = DNSResourceRecord{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DNSResourceRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSResourceRecord) ProtoMessage() {}

func (x *DNSResourceRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSResourceRecord.ProtoReflect.Descriptor instead.
func (*DNSResourceRecord) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{20}
}

func (x *DNSResourceRecord) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DNSResourceRecord) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DNSResourceRecord) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

func (x *DNSResourceRecord) GetTtl() uint32 {
	if x != nil {
		return x.Ttl
	}
	return 0
}

func (x *DNSResourceRecord) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DNSResourceRecord) GetNs() string {
	if x != nil {
		return x.Ns
	}
	return ""
}

func (x *DNSResourceRecord) GetCname() string {
	if x != nil {
		return x.Cname
	}
	return ""
}

func (x *DNSResourceRecord) GetPtr() string {
	if x != nil {
		return x.Ptr
	}
	return ""
}

func (x *DNSResourceRecord) GetTxts() []string {
	if x != nil {
		return x.Txts
	}
	return nil
}

func (x *DNSResourceRecord) GetSoa() *DNSSOA {
	if x != nil {
		return x.Soa
	}
	return nil
}

func (x *DNSResourceRecord) GetSrv() *DNSSRV {
	if x != nil {
		return x.Srv
	}
	return nil
}

func (x *DNSResourceRecord) GetMx() *DNSMX {
	if x != nil {
		return x.Mx
	}
	return nil
}

func (x *DNSResourceRecord) GetOpt() []*DNSOPT {
	if x != nil {
		return x.Opt
	}
	return nil
}

func (x *DNSResourceRecord) GetUri() *DNSURI {
	if x != nil {
		return x.Uri
	}
	return nil
}

func (x *DNSResourceRecord) GetTxt() string {
	if x != nil {
		return x.Txt
	}
	return ""
}

type DNSSOA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mname   string `protobuf:"bytes,1,opt,name=mname,proto3" json:"mname,omitempty"`
	Rname   string `protobuf:"bytes,2,opt,name=rname,proto3" json:"rname,omitempty"`
	Serial  uint32 `protobuf:"varint,3,opt,name=serial,proto3" json:"serial,omitempty"`
	Refresh uint32 `protobuf:"varint,4,opt,name=refresh,proto3" json:"refresh,omitempty"`
	Retry   uint32 `protobuf:"varint,5,opt,name=retry,proto3" json:"retry,omitempty"`
	Expire  uint32 `protobuf:"varint,6,opt,name=expire,proto3" json:"expire,omitempty"`
	Minimum uint32 `protobuf:"varint,7,opt,name=minimum,proto3" json:"minimum,omitempty"`
}

func (x *DNSSOA) Reset() {
	*x = DNSSOA{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DNSSOA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSSOA) ProtoMessage() {}

func (x *DNSSOA) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSSOA.ProtoReflect.Descriptor instead.
func (*DNSSOA) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{21}
}

func (x *DNSSOA) GetMname() string {
	if x != nil {
		return x.Mname
	}
	return ""
}

func (x *DNSSOA) GetRname() string {
	if x != nil {
		return x.Rname
	}
	return ""
}

func (x *DNSSOA) GetSerial() uint32 {
	if x != nil {
		return x.Serial
	}
	return 0
}

func (x *DNSSOA) GetRefresh() uint32 {
	if x != nil {
		return x.Refresh
	}
	return 0
}

func (x *DNSSOA) GetRetry() uint32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *DNSSOA) GetExpire() uint32 {
	if x != nil {
		return x.Expire
	}
	return 0
}

func (x *DNSSOA) GetMinimum() uint32 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

type DNSSRV struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Priority uint32 `protobuf:"varint,1,opt,name=priority,proto3" json:"priority,omitempty"`
	Weight   uint32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	Port     uint32 `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	Name     string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DNSSRV) Reset() {
	*x = DNSSRV{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DNSSRV) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSSRV) ProtoMessage() {}

func (x *DNSSRV) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSSRV.ProtoReflect.Descriptor instead.
func (*DNSSRV) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{22}
}

func (x *DNSSRV) GetPriority() uint32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *DNSSRV) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *DNSSRV) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *DNSSRV) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DNSMX struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Preference uint32 `protobuf:"varint,1,opt,name=Preference,proto3" json:"Preference,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DNSMX) Reset() {
	*x = DNSMX{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DNSMX) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSMX) ProtoMessage() {}

func (x *DNSMX) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSMX.ProtoReflect.Descriptor instead.
func (*DNSMX) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{23}
}

func (x *DNSMX) GetPreference() uint32 {
	if x != nil {
		return x.Preference
	}
	return 0
}

func (x *DNSMX) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DNSURI struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Priority uint32 `protobuf:"varint,1,opt,name=priority,proto3" json:"priority,omitempty"`
	Weight   uint32 `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	Target   string `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
}

func (x *DNSURI) Reset() {
	*x = DNSURI{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DNSURI) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSURI) ProtoMessage() {}

func (x *DNSURI) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSURI.ProtoReflect.Descriptor instead.
func (*DNSURI) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{24}
}

func (x *DNSURI) GetPriority() uint32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *DNSURI) GetWeight() uint32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *DNSURI) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

type DNSOPT struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Data string `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DNSOPT) Reset() {
	*x = DNSOPT{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DNSOPT) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNSOPT) ProtoMessage() {}

func (x *DNSOPT) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNSOPT.ProtoReflect.Descriptor instead.
func (*DNSOPT) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{25}
}

func (x *DNSOPT) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DNSOPT) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type HTTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Direction     string                 `protobuf:"bytes,1,opt,name=direction,proto3" json:"direction,omitempty"`
	Method        string                 `protobuf:"bytes,2,opt,name=method,proto3" json:"method,omitempty"`
	Protocol      string                 `protobuf:"bytes,3,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Host          string                 `protobuf:"bytes,4,opt,name=host,proto3" json:"host,omitempty"`
	UriPath       string                 `protobuf:"bytes,5,opt,name=uri_path,json=uriPath,proto3" json:"uri_path,omitempty"`
	Status        string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	StatusCode    int32                  `protobuf:"varint,7,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	Headers       map[string]*HttpHeader `protobuf:"bytes,8,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ContentLength int64                  `protobuf:"varint,9,opt,name=content_length,json=contentLength,proto3" json:"content_length,omitempty"`
}

func (x *HTTP) Reset() {
	*x = HTTP{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTP) ProtoMessage() {}

func (x *HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTP.ProtoReflect.Descriptor instead.
func (*HTTP) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{26}
}

func (x *HTTP) GetDirection() string {
	if x != nil {
		return x.Direction
	}
	return ""
}

func (x *HTTP) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *HTTP) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *HTTP) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *HTTP) GetUriPath() string {
	if x != nil {
		return x.UriPath
	}
	return ""
}

func (x *HTTP) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *HTTP) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *HTTP) GetHeaders() map[string]*HttpHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HTTP) GetContentLength() int64 {
	if x != nil {
		return x.ContentLength
	}
	return 0
}

type HTTPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method        string                 `protobuf:"bytes,1,opt,name=method,proto3" json:"method,omitempty"`
	Protocol      string                 `protobuf:"bytes,3,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Host          string                 `protobuf:"bytes,4,opt,name=host,proto3" json:"host,omitempty"`
	UriPath       string                 `protobuf:"bytes,5,opt,name=uri_path,json=uriPath,proto3" json:"uri_path,omitempty"`
	Headers       map[string]*HttpHeader `protobuf:"bytes,6,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ContentLength int64                  `protobuf:"varint,7,opt,name=content_length,json=contentLength,proto3" json:"content_length,omitempty"`
}

func (x *HTTPRequest) Reset() {
	*x = HTTPRequest{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HTTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPRequest) ProtoMessage() {}

func (x *HTTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPRequest.ProtoReflect.Descriptor instead.
func (*HTTPRequest) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{27}
}

func (x *HTTPRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *HTTPRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *HTTPRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *HTTPRequest) GetUriPath() string {
	if x != nil {
		return x.UriPath
	}
	return ""
}

func (x *HTTPRequest) GetHeaders() map[string]*HttpHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HTTPRequest) GetContentLength() int64 {
	if x != nil {
		return x.ContentLength
	}
	return 0
}

type HTTPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	StatusCode    int32                  `protobuf:"varint,2,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	Protocol      string                 `protobuf:"bytes,3,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Headers       map[string]*HttpHeader `protobuf:"bytes,4,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ContentLength int64                  `protobuf:"varint,5,opt,name=content_length,json=contentLength,proto3" json:"content_length,omitempty"`
}

func (x *HTTPResponse) Reset() {
	*x = HTTPResponse{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HTTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPResponse) ProtoMessage() {}

func (x *HTTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPResponse.ProtoReflect.Descriptor instead.
func (*HTTPResponse) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{28}
}

func (x *HTTPResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *HTTPResponse) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *HTTPResponse) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *HTTPResponse) GetHeaders() map[string]*HttpHeader {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HTTPResponse) GetContentLength() int64 {
	if x != nil {
		return x.ContentLength
	}
	return 0
}

type HttpHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header []string `protobuf:"bytes,1,rep,name=header,proto3" json:"header,omitempty"`
}

func (x *HttpHeader) Reset() {
	*x = HttpHeader{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpHeader) ProtoMessage() {}

func (x *HttpHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpHeader.ProtoReflect.Descriptor instead.
func (*HttpHeader) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{29}
}

func (x *HttpHeader) GetHeader() []string {
	if x != nil {
		return x.Header
	}
	return nil
}

type PacketMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrcIp     string          `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	DstIp     string          `protobuf:"bytes,2,opt,name=dst_ip,json=dstIp,proto3" json:"dst_ip,omitempty"`
	SrcPort   uint32          `protobuf:"varint,3,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`
	DstPort   uint32          `protobuf:"varint,4,opt,name=dst_port,json=dstPort,proto3" json:"dst_port,omitempty"`
	Protocol  uint32          `protobuf:"varint,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
	PacketLen uint32          `protobuf:"varint,6,opt,name=packet_len,json=packetLen,proto3" json:"packet_len,omitempty"`
	Iface     string          `protobuf:"bytes,7,opt,name=iface,proto3" json:"iface,omitempty"`
	Direction PacketDirection `protobuf:"varint,8,opt,name=direction,proto3,enum=tracee.v1beta1.PacketDirection" json:"direction,omitempty"`
}

func (x *PacketMetadata) Reset() {
	*x = PacketMetadata{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PacketMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PacketMetadata) ProtoMessage() {}

func (x *PacketMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PacketMetadata.ProtoReflect.Descriptor instead.
func (*PacketMetadata) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{30}
}

func (x *PacketMetadata) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *PacketMetadata) GetDstIp() string {
	if x != nil {
		return x.DstIp
	}
	return ""
}

func (x *PacketMetadata) GetSrcPort() uint32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *PacketMetadata) GetDstPort() uint32 {
	if x != nil {
		return x.DstPort
	}
	return 0
}

func (x *PacketMetadata) GetProtocol() uint32 {
	if x != nil {
		return x.Protocol
	}
	return 0
}

func (x *PacketMetadata) GetPacketLen() uint32 {
	if x != nil {
		return x.PacketLen
	}
	return 0
}

func (x *PacketMetadata) GetIface() string {
	if x != nil {
		return x.Iface
	}
	return ""
}

func (x *PacketMetadata) GetDirection() PacketDirection {
	if x != nil {
		return x.Direction
	}
	return PacketDirection_INVALID
}

type DnsQueryData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query      string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	QueryType  string `protobuf:"bytes,2,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	QueryClass string `protobuf:"bytes,3,opt,name=query_class,json=queryClass,proto3" json:"query_class,omitempty"`
}

func (x *DnsQueryData) Reset() {
	*x = DnsQueryData{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsQueryData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsQueryData) ProtoMessage() {}

func (x *DnsQueryData) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsQueryData.ProtoReflect.Descriptor instead.
func (*DnsQueryData) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{31}
}

func (x *DnsQueryData) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *DnsQueryData) GetQueryType() string {
	if x != nil {
		return x.QueryType
	}
	return ""
}

func (x *DnsQueryData) GetQueryClass() string {
	if x != nil {
		return x.QueryClass
	}
	return ""
}

type DnsAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Ttl    uint32 `protobuf:"varint,2,opt,name=ttl,proto3" json:"ttl,omitempty"`
	Answer string `protobuf:"bytes,3,opt,name=answer,proto3" json:"answer,omitempty"`
}

func (x *DnsAnswer) Reset() {
	*x = DnsAnswer{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsAnswer) ProtoMessage() {}

func (x *DnsAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsAnswer.ProtoReflect.Descriptor instead.
func (*DnsAnswer) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{32}
}

func (x *DnsAnswer) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DnsAnswer) GetTtl() uint32 {
	if x != nil {
		return x.Ttl
	}
	return 0
}

func (x *DnsAnswer) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

type DnsResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DnsQueryData *DnsQueryData `protobuf:"bytes,1,opt,name=dns_query_data,json=dnsQueryData,proto3" json:"dns_query_data,omitempty"`
	DnsAnswer    []*DnsAnswer  `protobuf:"bytes,2,rep,name=dns_answer,json=dnsAnswer,proto3" json:"dns_answer,omitempty"`
}

func (x *DnsResponseData) Reset() {
	*x = DnsResponseData{}
	mi := &file_api_v1beta1_event_data_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsResponseData) ProtoMessage() {}

func (x *DnsResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_event_data_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsResponseData.ProtoReflect.Descriptor instead.
func (*DnsResponseData) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_event_data_proto_rawDescGZIP(), []int{33}
}

func (x *DnsResponseData) GetDnsQueryData() *DnsQueryData {
	if x != nil {
		return x.DnsQueryData
	}
	return nil
}

func (x *DnsResponseData) GetDnsAnswer() []*DnsAnswer {
	if x != nil {
		return x.DnsAnswer
	}
	return nil
}

var File_api_v1beta1_event_data_proto protoreflect.FileDescriptor

var file_api_v1beta1_event_data_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbd, 0x0b, 0x0a,
	0x0a, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x05, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00,
	0x52, 0x05, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x16, 0x0a, 0x05, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x19, 0x0a, 0x07, 0x75, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x48, 0x00, 0x52, 0x06, 0x75, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x19, 0x0a, 0x07, 0x75, 0x5f,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x06, 0x75,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x12, 0x0a, 0x03, 0x73, 0x74, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x73, 0x74, 0x72, 0x12, 0x16, 0x0a, 0x05, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x05, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x00, 0x52, 0x04, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x3a, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x5f, 0x61,
	0x72, 0x72, 0x61, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x72, 0x61,
	0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x48, 0x00, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x72,
	0x72, 0x61, 0x79, 0x12, 0x3d, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x61, 0x72, 0x72,
	0x61, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x41,
	0x72, 0x72, 0x61, 0x79, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x41, 0x72, 0x72,
	0x61, 0x79, 0x12, 0x41, 0x0a, 0x0d, 0x75, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x61, 0x72,
	0x72, 0x61, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x41, 0x72, 0x72, 0x61, 0x79, 0x48, 0x00, 0x52, 0x0b, 0x75, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x36, 0x0a, 0x08, 0x73, 0x6f, 0x63, 0x6b, 0x61, 0x64, 0x64,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x53, 0x6f, 0x63, 0x6b, 0x41, 0x64, 0x64,
	0x72, 0x48, 0x00, 0x52, 0x08, 0x73, 0x6f, 0x63, 0x6b, 0x61, 0x64, 0x64, 0x72, 0x12, 0x3f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x48,
	0x00, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x36,
	0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x70, 0x65, 0x63, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x70, 0x65, 0x63, 0x48, 0x00, 0x52, 0x08, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x70, 0x65, 0x63, 0x12, 0x49, 0x0a, 0x0f, 0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x64,
	0x5f, 0x73, 0x79, 0x73, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x53, 0x79, 0x73, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x48,
	0x00, 0x52, 0x0e, 0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x53, 0x79, 0x73, 0x63, 0x61, 0x6c, 0x6c,
	0x73, 0x12, 0x44, 0x0a, 0x0e, 0x68, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x71, 0x5f,
	0x6f, 0x70, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x65,
	0x64, 0x53, 0x65, 0x71, 0x4f, 0x70, 0x73, 0x48, 0x00, 0x52, 0x0c, 0x68, 0x6f, 0x6f, 0x6b, 0x65,
	0x64, 0x53, 0x65, 0x71, 0x4f, 0x70, 0x73, 0x12, 0x2a, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x49, 0x50, 0x76, 0x34, 0x48, 0x00, 0x52, 0x04, 0x69,
	0x70, 0x76, 0x34, 0x12, 0x2a, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x49, 0x50, 0x76, 0x36, 0x48, 0x00, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12,
	0x27, 0x0a, 0x03, 0x74, 0x63, 0x70, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x54, 0x43,
	0x50, 0x48, 0x00, 0x52, 0x03, 0x74, 0x63, 0x70, 0x12, 0x27, 0x0a, 0x03, 0x75, 0x64, 0x70, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x55, 0x44, 0x50, 0x48, 0x00, 0x52, 0x03, 0x75, 0x64,
	0x70, 0x12, 0x2a, 0x0a, 0x04, 0x69, 0x63, 0x6d, 0x70, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x49, 0x43, 0x4d, 0x50, 0x48, 0x00, 0x52, 0x04, 0x69, 0x63, 0x6d, 0x70, 0x12, 0x30, 0x0a,
	0x06, 0x69, 0x63, 0x6d, 0x70, 0x76, 0x36, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x49,
	0x43, 0x4d, 0x50, 0x76, 0x36, 0x48, 0x00, 0x52, 0x06, 0x69, 0x63, 0x6d, 0x70, 0x76, 0x36, 0x12,
	0x27, 0x0a, 0x03, 0x64, 0x6e, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x4e,
	0x53, 0x48, 0x00, 0x52, 0x03, 0x64, 0x6e, 0x73, 0x12, 0x43, 0x0a, 0x0d, 0x64, 0x6e, 0x73, 0x5f,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x44, 0x6e, 0x73, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52,
	0x0c, 0x64, 0x6e, 0x73, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x0a,
	0x0d, 0x64, 0x6e, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x73, 0x48, 0x00, 0x52, 0x0c, 0x64, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x50, 0x61, 0x63,
	0x6b, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a,
	0x04, 0x68, 0x74, 0x74, 0x70, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54,
	0x50, 0x48, 0x00, 0x52, 0x04, 0x68, 0x74, 0x74, 0x70, 0x12, 0x40, 0x0a, 0x0c, 0x68, 0x74, 0x74,
	0x70, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0b,
	0x68, 0x74, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0d, 0x68,
	0x74, 0x74, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x00, 0x52, 0x0c, 0x68, 0x74, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a, 0x0b,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x22, 0x0a, 0x0a, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x23, 0x0a, 0x0b, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x41,
	0x72, 0x72, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xe4, 0x06, 0x0a, 0x0b, 0x43,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x03, 0x67, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x67, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x73, 0x75,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x73, 0x75, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x04,
	0x73, 0x67, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x73, 0x67, 0x69, 0x64, 0x12, 0x30,
	0x0a, 0x04, 0x65, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x65, 0x75, 0x69, 0x64,
	0x12, 0x30, 0x0a, 0x04, 0x65, 0x67, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x65, 0x67,
	0x69, 0x64, 0x12, 0x32, 0x0a, 0x05, 0x66, 0x73, 0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x05, 0x66, 0x73, 0x75, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x05, 0x66, 0x73, 0x67, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x05, 0x66, 0x73, 0x67, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x0e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x3d, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x5f, 0x62, 0x69, 0x74, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0a, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x42, 0x69, 0x74, 0x73, 0x12, 0x43,
	0x0a, 0x0f, 0x63, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x68, 0x65, 0x72, 0x69, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x52, 0x0e, 0x63, 0x61, 0x70, 0x49, 0x6e, 0x68, 0x65, 0x72, 0x69, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x61, 0x70, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x74, 0x74, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x72, 0x61,
	0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0c, 0x63, 0x61, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x74, 0x74, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x0d, 0x63, 0x61, 0x70, 0x5f, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x61, 0x70,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0c, 0x63, 0x61, 0x70, 0x45, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x5f, 0x62, 0x6f, 0x75,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x61, 0x70,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0b, 0x63, 0x61, 0x70, 0x42, 0x6f, 0x75, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x61, 0x70, 0x5f, 0x61, 0x6d, 0x62, 0x69,
	0x65, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x70, 0x41, 0x6d, 0x62, 0x69, 0x65, 0x6e,
	0x74, 0x22, 0x3e, 0x0a, 0x08, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x70, 0x65, 0x63, 0x12, 0x32, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x97, 0x02, 0x0a, 0x08, 0x53, 0x6f, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x12, 0x38,
	0x0a, 0x09, 0x73, 0x61, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x73, 0x61, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x74, 0x52, 0x08,
	0x73, 0x61, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x6e, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x6e, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x69, 0x6e, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x73, 0x69, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x6e,
	0x36, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69,
	0x6e, 0x36, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x6e, 0x36, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x69, 0x6e, 0x36, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x6e, 0x36, 0x5f, 0x66, 0x6c, 0x6f, 0x77,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73, 0x69, 0x6e, 0x36,
	0x46, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x6e, 0x36,
	0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x73, 0x69, 0x6e, 0x36, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x69, 0x64, 0x22, 0x48, 0x0a, 0x0e, 0x48,
	0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x53, 0x79, 0x73, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x36, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x6f,
	0x6f, 0x6b, 0x65, 0x64, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x56, 0x0a, 0x10, 0x48, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x53,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0xa9, 0x01,
	0x0a, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x53, 0x65, 0x71, 0x4f, 0x70, 0x73, 0x12, 0x3d,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48,
	0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x53, 0x65, 0x71, 0x4f, 0x70, 0x73, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x5a, 0x0a,
	0x0a, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x6f,
	0x6f, 0x6b, 0x65, 0x64, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9b, 0x02, 0x0a, 0x04, 0x49, 0x50,
	0x76, 0x34, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x69, 0x68, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x69, 0x68, 0x6c, 0x12, 0x10,
	0x0a, 0x03, 0x74, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x6f, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6c, 0x61, 0x67,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x66, 0x72, 0x61, 0x67, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x66, 0x72, 0x61, 0x67, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x74,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x72, 0x63,
	0x5f, 0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x72, 0x63, 0x49, 0x70,
	0x12, 0x15, 0x0a, 0x06, 0x64, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x64, 0x73, 0x74, 0x49, 0x70, 0x22, 0xe8, 0x01, 0x0a, 0x04, 0x49, 0x50, 0x76, 0x36,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x72,
	0x61, 0x66, 0x66, 0x69, 0x63, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x70, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x68, 0x6f, 0x70, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x72, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x72, 0x63, 0x49, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x64,
	0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x73, 0x74,
	0x49, 0x70, 0x22, 0xbd, 0x03, 0x0a, 0x03, 0x54, 0x43, 0x50, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72,
	0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x72,
	0x63, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73,
	0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x61, 0x63, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x4f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x5f, 0x66, 0x6c, 0x61,
	0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x66, 0x69, 0x6e, 0x46, 0x6c, 0x61, 0x67,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x79, 0x6e, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x73, 0x79, 0x6e, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x72,
	0x73, 0x74, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72,
	0x73, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x73, 0x68, 0x5f, 0x66, 0x6c,
	0x61, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x73, 0x68, 0x46, 0x6c, 0x61,
	0x67, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x6b, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x63, 0x6b, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08,
	0x75, 0x72, 0x67, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x75, 0x72, 0x67, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x63, 0x65, 0x5f, 0x66,
	0x6c, 0x61, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x65, 0x63, 0x65, 0x46, 0x6c,
	0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x77, 0x72, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x63, 0x77, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x17, 0x0a,
	0x07, 0x6e, 0x73, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x6e, 0x73, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x72,
	0x67, 0x65, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x72, 0x67, 0x65,
	0x6e, 0x74, 0x22, 0x6f, 0x0a, 0x03, 0x55, 0x44, 0x50, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63,
	0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x72, 0x63,
	0x50, 0x6f, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x22, 0x61, 0x0a, 0x04, 0x49, 0x43, 0x4d, 0x50, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x73, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x73, 0x75, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x22, 0x41, 0x0a, 0x06, 0x49, 0x43, 0x4d, 0x50, 0x76, 0x36,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x22, 0x4a, 0x0a, 0x0c, 0x44, 0x6e, 0x73,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3a, 0x0a, 0x09, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x6e,
	0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x4d, 0x0a, 0x0c, 0x44, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x73, 0x22, 0x9f, 0x04, 0x0a, 0x03, 0x44, 0x4e, 0x53, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x71, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x71, 0x72, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x61, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x61, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x74, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x72, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x02, 0x72, 0x61, 0x12, 0x0c, 0x0a, 0x01, 0x7a, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x01, 0x7a, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x71, 0x64, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x71, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x72, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x4e, 0x53, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x3b, 0x0a, 0x07, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x31, 0x2e, 0x44, 0x4e, 0x53, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x07, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x12, 0x43, 0x0a, 0x0b,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x44, 0x4e, 0x53, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x43, 0x0a, 0x0b, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x73,
	0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x4e, 0x53, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x73, 0x22, 0x4b, 0x0a, 0x0b, 0x44, 0x4e, 0x53, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x22, 0xa0, 0x03, 0x0a, 0x11, 0x44, 0x4e, 0x53, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x74,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x78, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x78, 0x74, 0x73, 0x12, 0x28, 0x0a, 0x03, 0x73, 0x6f, 0x61, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x44, 0x4e, 0x53, 0x53, 0x4f, 0x41, 0x52, 0x03, 0x73, 0x6f, 0x61, 0x12,
	0x28, 0x0a, 0x03, 0x73, 0x72, 0x76, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x4e,
	0x53, 0x53, 0x52, 0x56, 0x52, 0x03, 0x73, 0x72, 0x76, 0x12, 0x25, 0x0a, 0x02, 0x6d, 0x78, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x4e, 0x53, 0x4d, 0x58, 0x52, 0x02, 0x6d, 0x78,
	0x12, 0x28, 0x0a, 0x03, 0x6f, 0x70, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44,
	0x4e, 0x53, 0x4f, 0x50, 0x54, 0x52, 0x03, 0x6f, 0x70, 0x74, 0x12, 0x28, 0x0a, 0x03, 0x75, 0x72,
	0x69, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x4e, 0x53, 0x55, 0x52, 0x49, 0x52,
	0x03, 0x75, 0x72, 0x69, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x78, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x78, 0x74, 0x22, 0xae, 0x01, 0x0a, 0x06, 0x44, 0x4e, 0x53, 0x53, 0x4f,
	0x41, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x22, 0x64, 0x0a, 0x06, 0x44, 0x4e, 0x53, 0x53, 0x52,
	0x56, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x3b, 0x0a,
	0x05, 0x44, 0x4e, 0x53, 0x4d, 0x58, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x06, 0x44, 0x4e,
	0x53, 0x55, 0x52, 0x49, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x22, 0x30, 0x0a, 0x06, 0x44, 0x4e, 0x53, 0x4f, 0x50, 0x54, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0xfc, 0x02, 0x0a, 0x04, 0x48, 0x54, 0x54, 0x50, 0x12, 0x1c, 0x0a, 0x09, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x72, 0x69, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x72, 0x69, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x1a, 0x56, 0x0a, 0x0c, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x72, 0x61,
	0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x74, 0x74, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xb3, 0x02, 0x0a, 0x0b, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x72, 0x69,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x72, 0x69,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x42, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x1a,
	0x56, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x31, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa7, 0x02, 0x0a, 0x0c, 0x48, 0x54, 0x54, 0x50,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x43, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e,
	0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x65,
	0x6e, 0x67, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x1a, 0x56, 0x0a, 0x0c, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x72, 0x61,
	0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x48, 0x74, 0x74, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x24, 0x0a, 0x0a, 0x48, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x84, 0x02, 0x0a, 0x0e, 0x50, 0x61, 0x63, 0x6b,
	0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x72,
	0x63, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x72, 0x63, 0x49,
	0x70, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x64, 0x73, 0x74, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x72, 0x63, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6c, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x4c, 0x65, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x66, 0x61,
	0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x66, 0x61, 0x63, 0x65, 0x12,
	0x3d, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x64,
	0x0a, 0x0c, 0x44, 0x6e, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14,
	0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x75, 0x65, 0x72, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x22, 0x49, 0x0a, 0x09, 0x44, 0x6e, 0x73, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x22,
	0x8f, 0x01, 0x0a, 0x0f, 0x44, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0e, 0x64, 0x6e, 0x73, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x6e, 0x73,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x64, 0x6e, 0x73, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x0a, 0x64, 0x6e, 0x73, 0x5f, 0x61,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x44, 0x6e, 0x73,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x09, 0x64, 0x6e, 0x73, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x2a, 0x4d, 0x0a, 0x0b, 0x73, 0x61, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x5f, 0x74,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x41, 0x5f, 0x46, 0x41, 0x4d, 0x49, 0x4c, 0x59, 0x5f, 0x54, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x46, 0x5f, 0x55,
	0x4e, 0x49, 0x58, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x46, 0x5f, 0x49, 0x4e, 0x45, 0x54,
	0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x46, 0x5f, 0x49, 0x4e, 0x45, 0x54, 0x36, 0x10, 0x0a,
	0x2a, 0x85, 0x06, 0x0a, 0x0a, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x0d, 0x0a, 0x09, 0x43, 0x41, 0x50, 0x5f, 0x43, 0x48, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10,
	0x0a, 0x0c, 0x44, 0x41, 0x43, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x10, 0x01,
	0x12, 0x17, 0x0a, 0x13, 0x43, 0x41, 0x50, 0x5f, 0x44, 0x41, 0x43, 0x5f, 0x52, 0x45, 0x41, 0x44,
	0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x50,
	0x5f, 0x46, 0x4f, 0x57, 0x4e, 0x45, 0x52, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x50,
	0x5f, 0x46, 0x53, 0x45, 0x54, 0x49, 0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x50,
	0x5f, 0x4b, 0x49, 0x4c, 0x4c, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x50, 0x5f, 0x53,
	0x45, 0x54, 0x47, 0x49, 0x44, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x50, 0x5f, 0x53,
	0x45, 0x54, 0x55, 0x49, 0x44, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x41, 0x50, 0x5f, 0x53,
	0x45, 0x54, 0x50, 0x43, 0x41, 0x50, 0x10, 0x08, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x50, 0x5f,
	0x4e, 0x45, 0x54, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x10, 0x0a, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x50, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x42, 0x52,
	0x4f, 0x41, 0x44, 0x43, 0x41, 0x53, 0x54, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x50,
	0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b,
	0x43, 0x41, 0x50, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x52, 0x41, 0x57, 0x10, 0x0d, 0x12, 0x10, 0x0a,
	0x0c, 0x43, 0x41, 0x50, 0x5f, 0x49, 0x50, 0x43, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x0e, 0x12,
	0x11, 0x0a, 0x0d, 0x43, 0x41, 0x50, 0x5f, 0x49, 0x50, 0x43, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52,
	0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x5f, 0x4d, 0x4f,
	0x44, 0x55, 0x4c, 0x45, 0x10, 0x10, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x59,
	0x53, 0x5f, 0x52, 0x41, 0x57, 0x49, 0x4f, 0x10, 0x11, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x50,
	0x5f, 0x53, 0x59, 0x53, 0x5f, 0x43, 0x48, 0x52, 0x4f, 0x4f, 0x54, 0x10, 0x12, 0x12, 0x12, 0x0a,
	0x0e, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x5f, 0x50, 0x54, 0x52, 0x41, 0x43, 0x45, 0x10,
	0x13, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x5f, 0x50, 0x41, 0x43,
	0x43, 0x54, 0x10, 0x14, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x5f,
	0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x15, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x41, 0x50, 0x5f, 0x53,
	0x59, 0x53, 0x5f, 0x42, 0x4f, 0x4f, 0x54, 0x10, 0x16, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x41, 0x50,
	0x5f, 0x53, 0x59, 0x53, 0x5f, 0x4e, 0x49, 0x43, 0x45, 0x10, 0x17, 0x12, 0x14, 0x0a, 0x10, 0x43,
	0x41, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x10,
	0x18, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x10, 0x19, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x5f, 0x54,
	0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0x1a, 0x12, 0x0d, 0x0a, 0x09, 0x43,
	0x41, 0x50, 0x5f, 0x4d, 0x4b, 0x4e, 0x4f, 0x44, 0x10, 0x1b, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41,
	0x50, 0x5f, 0x4c, 0x45, 0x41, 0x53, 0x45, 0x10, 0x1c, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x50,
	0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x10, 0x1d, 0x12, 0x15,
	0x0a, 0x11, 0x43, 0x41, 0x50, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54,
	0x52, 0x4f, 0x4c, 0x10, 0x1e, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x45, 0x54,
	0x46, 0x43, 0x41, 0x50, 0x10, 0x1f, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x41, 0x50, 0x5f, 0x4d, 0x41,
	0x43, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x10, 0x20, 0x12, 0x11, 0x0a, 0x0d,
	0x43, 0x41, 0x50, 0x5f, 0x4d, 0x41, 0x43, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x21, 0x12,
	0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x4c, 0x4f, 0x47, 0x10, 0x22, 0x12,
	0x12, 0x0a, 0x0e, 0x43, 0x41, 0x50, 0x5f, 0x57, 0x41, 0x4b, 0x45, 0x5f, 0x41, 0x4c, 0x41, 0x52,
	0x4d, 0x10, 0x23, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x50, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b,
	0x5f, 0x53, 0x55, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x10, 0x24, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41,
	0x50, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x10, 0x25, 0x12, 0x0f,
	0x0a, 0x0b, 0x43, 0x41, 0x50, 0x5f, 0x50, 0x45, 0x52, 0x46, 0x4d, 0x4f, 0x4e, 0x10, 0x26, 0x12,
	0x0b, 0x0a, 0x07, 0x43, 0x41, 0x50, 0x5f, 0x42, 0x50, 0x46, 0x10, 0x27, 0x12, 0x1a, 0x0a, 0x16,
	0x43, 0x41, 0x50, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0x28, 0x2a, 0x37, 0x0a, 0x0f, 0x50, 0x61, 0x63, 0x6b,
	0x65, 0x74, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10,
	0x03, 0x42, 0x2b, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x2f, 0x61,
	0x71, 0x75, 0x61, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2f, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_v1beta1_event_data_proto_rawDescOnce sync.Once
	file_api_v1beta1_event_data_proto_rawDescData = file_api_v1beta1_event_data_proto_rawDesc
)

func file_api_v1beta1_event_data_proto_rawDescGZIP() []byte {
	file_api_v1beta1_event_data_proto_rawDescOnce.Do(func() {
		file_api_v1beta1_event_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_v1beta1_event_data_proto_rawDescData)
	})
	return file_api_v1beta1_event_data_proto_rawDescData
}

var file_api_v1beta1_event_data_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_v1beta1_event_data_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_api_v1beta1_event_data_proto_goTypes = []any{
	(SaFamilyT)(0),                 // 0: tracee.v1beta1.sa_family_t
	(Capability)(0),                // 1: tracee.v1beta1.Capability
	(PacketDirection)(0),           // 2: tracee.v1beta1.PacketDirection
	(*EventValue)(nil),             // 3: tracee.v1beta1.EventValue
	(*StringArray)(nil),            // 4: tracee.v1beta1.StringArray
	(*Int32Array)(nil),             // 5: tracee.v1beta1.Int32Array
	(*UInt64Array)(nil),            // 6: tracee.v1beta1.UInt64Array
	(*Credentials)(nil),            // 7: tracee.v1beta1.Credentials
	(*Timespec)(nil),               // 8: tracee.v1beta1.Timespec
	(*SockAddr)(nil),               // 9: tracee.v1beta1.SockAddr
	(*HookedSyscalls)(nil),         // 10: tracee.v1beta1.HookedSyscalls
	(*HookedSymbolData)(nil),       // 11: tracee.v1beta1.HookedSymbolData
	(*HookedSeqOps)(nil),           // 12: tracee.v1beta1.HookedSeqOps
	(*IPv4)(nil),                   // 13: tracee.v1beta1.IPv4
	(*IPv6)(nil),                   // 14: tracee.v1beta1.IPv6
	(*TCP)(nil),                    // 15: tracee.v1beta1.TCP
	(*UDP)(nil),                    // 16: tracee.v1beta1.UDP
	(*ICMP)(nil),                   // 17: tracee.v1beta1.ICMP
	(*ICMPv6)(nil),                 // 18: tracee.v1beta1.ICMPv6
	(*DnsQuestions)(nil),           // 19: tracee.v1beta1.DnsQuestions
	(*DnsResponses)(nil),           // 20: tracee.v1beta1.DnsResponses
	(*DNS)(nil),                    // 21: tracee.v1beta1.DNS
	(*DNSQuestion)(nil),            // 22: tracee.v1beta1.DNSQuestion
	(*DNSResourceRecord)(nil),      // 23: tracee.v1beta1.DNSResourceRecord
	(*DNSSOA)(nil),                 // 24: tracee.v1beta1.DNSSOA
	(*DNSSRV)(nil),                 // 25: tracee.v1beta1.DNSSRV
	(*DNSMX)(nil),                  // 26: tracee.v1beta1.DNSMX
	(*DNSURI)(nil),                 // 27: tracee.v1beta1.DNSURI
	(*DNSOPT)(nil),                 // 28: tracee.v1beta1.DNSOPT
	(*HTTP)(nil),                   // 29: tracee.v1beta1.HTTP
	(*HTTPRequest)(nil),            // 30: tracee.v1beta1.HTTPRequest
	(*HTTPResponse)(nil),           // 31: tracee.v1beta1.HTTPResponse
	(*HttpHeader)(nil),             // 32: tracee.v1beta1.HttpHeader
	(*PacketMetadata)(nil),         // 33: tracee.v1beta1.PacketMetadata
	(*DnsQueryData)(nil),           // 34: tracee.v1beta1.DnsQueryData
	(*DnsAnswer)(nil),              // 35: tracee.v1beta1.DnsAnswer
	(*DnsResponseData)(nil),        // 36: tracee.v1beta1.DnsResponseData
	nil,                            // 37: tracee.v1beta1.HookedSeqOps.ValueEntry
	nil,                            // 38: tracee.v1beta1.HTTP.HeadersEntry
	nil,                            // 39: tracee.v1beta1.HTTPRequest.HeadersEntry
	nil,                            // 40: tracee.v1beta1.HTTPResponse.HeadersEntry
	(*structpb.Struct)(nil),        // 41: google.protobuf.Struct
	(*wrapperspb.UInt32Value)(nil), // 42: google.protobuf.UInt32Value
	(*wrapperspb.DoubleValue)(nil), // 43: google.protobuf.DoubleValue
}
var file_api_v1beta1_event_data_proto_depIdxs = []int32{
	4,  // 0: tracee.v1beta1.EventValue.str_array:type_name -> tracee.v1beta1.StringArray
	5,  // 1: tracee.v1beta1.EventValue.int32_array:type_name -> tracee.v1beta1.Int32Array
	6,  // 2: tracee.v1beta1.EventValue.u_int64_array:type_name -> tracee.v1beta1.UInt64Array
	9,  // 3: tracee.v1beta1.EventValue.sockaddr:type_name -> tracee.v1beta1.SockAddr
	7,  // 4: tracee.v1beta1.EventValue.credentials:type_name -> tracee.v1beta1.Credentials
	8,  // 5: tracee.v1beta1.EventValue.timespec:type_name -> tracee.v1beta1.Timespec
	10, // 6: tracee.v1beta1.EventValue.hooked_syscalls:type_name -> tracee.v1beta1.HookedSyscalls
	12, // 7: tracee.v1beta1.EventValue.hooked_seq_ops:type_name -> tracee.v1beta1.HookedSeqOps
	13, // 8: tracee.v1beta1.EventValue.ipv4:type_name -> tracee.v1beta1.IPv4
	14, // 9: tracee.v1beta1.EventValue.ipv6:type_name -> tracee.v1beta1.IPv6
	15, // 10: tracee.v1beta1.EventValue.tcp:type_name -> tracee.v1beta1.TCP
	16, // 11: tracee.v1beta1.EventValue.udp:type_name -> tracee.v1beta1.UDP
	17, // 12: tracee.v1beta1.EventValue.icmp:type_name -> tracee.v1beta1.ICMP
	18, // 13: tracee.v1beta1.EventValue.icmpv6:type_name -> tracee.v1beta1.ICMPv6
	21, // 14: tracee.v1beta1.EventValue.dns:type_name -> tracee.v1beta1.DNS
	19, // 15: tracee.v1beta1.EventValue.dns_questions:type_name -> tracee.v1beta1.DnsQuestions
	20, // 16: tracee.v1beta1.EventValue.dns_responses:type_name -> tracee.v1beta1.DnsResponses
	33, // 17: tracee.v1beta1.EventValue.packet_metadata:type_name -> tracee.v1beta1.PacketMetadata
	29, // 18: tracee.v1beta1.EventValue.http:type_name -> tracee.v1beta1.HTTP
	30, // 19: tracee.v1beta1.EventValue.http_request:type_name -> tracee.v1beta1.HTTPRequest
	31, // 20: tracee.v1beta1.EventValue.http_response:type_name -> tracee.v1beta1.HTTPResponse
	41, // 21: tracee.v1beta1.EventValue.struct:type_name -> google.protobuf.Struct
	42, // 22: tracee.v1beta1.Credentials.uid:type_name -> google.protobuf.UInt32Value
	42, // 23: tracee.v1beta1.Credentials.gid:type_name -> google.protobuf.UInt32Value
	42, // 24: tracee.v1beta1.Credentials.suid:type_name -> google.protobuf.UInt32Value
	42, // 25: tracee.v1beta1.Credentials.sgid:type_name -> google.protobuf.UInt32Value
	42, // 26: tracee.v1beta1.Credentials.euid:type_name -> google.protobuf.UInt32Value
	42, // 27: tracee.v1beta1.Credentials.egid:type_name -> google.protobuf.UInt32Value
	42, // 28: tracee.v1beta1.Credentials.fsuid:type_name -> google.protobuf.UInt32Value
	42, // 29: tracee.v1beta1.Credentials.fsgid:type_name -> google.protobuf.UInt32Value
	42, // 30: tracee.v1beta1.Credentials.user_namespace:type_name -> google.protobuf.UInt32Value
	42, // 31: tracee.v1beta1.Credentials.secure_bits:type_name -> google.protobuf.UInt32Value
	1,  // 32: tracee.v1beta1.Credentials.cap_inheritable:type_name -> tracee.v1beta1.Capability
	1,  // 33: tracee.v1beta1.Credentials.cap_permitted:type_name -> tracee.v1beta1.Capability
	1,  // 34: tracee.v1beta1.Credentials.cap_effective:type_name -> tracee.v1beta1.Capability
	1,  // 35: tracee.v1beta1.Credentials.cap_bounding:type_name -> tracee.v1beta1.Capability
	1,  // 36: tracee.v1beta1.Credentials.cap_ambient:type_name -> tracee.v1beta1.Capability
	43, // 37: tracee.v1beta1.Timespec.value:type_name -> google.protobuf.DoubleValue
	0,  // 38: tracee.v1beta1.SockAddr.sa_family:type_name -> tracee.v1beta1.sa_family_t
	11, // 39: tracee.v1beta1.HookedSyscalls.value:type_name -> tracee.v1beta1.HookedSymbolData
	37, // 40: tracee.v1beta1.HookedSeqOps.value:type_name -> tracee.v1beta1.HookedSeqOps.ValueEntry
	34, // 41: tracee.v1beta1.DnsQuestions.questions:type_name -> tracee.v1beta1.DnsQueryData
	36, // 42: tracee.v1beta1.DnsResponses.responses:type_name -> tracee.v1beta1.DnsResponseData
	22, // 43: tracee.v1beta1.DNS.questions:type_name -> tracee.v1beta1.DNSQuestion
	23, // 44: tracee.v1beta1.DNS.answers:type_name -> tracee.v1beta1.DNSResourceRecord
	23, // 45: tracee.v1beta1.DNS.authorities:type_name -> tracee.v1beta1.DNSResourceRecord
	23, // 46: tracee.v1beta1.DNS.additionals:type_name -> tracee.v1beta1.DNSResourceRecord
	24, // 47: tracee.v1beta1.DNSResourceRecord.soa:type_name -> tracee.v1beta1.DNSSOA
	25, // 48: tracee.v1beta1.DNSResourceRecord.srv:type_name -> tracee.v1beta1.DNSSRV
	26, // 49: tracee.v1beta1.DNSResourceRecord.mx:type_name -> tracee.v1beta1.DNSMX
	28, // 50: tracee.v1beta1.DNSResourceRecord.opt:type_name -> tracee.v1beta1.DNSOPT
	27, // 51: tracee.v1beta1.DNSResourceRecord.uri:type_name -> tracee.v1beta1.DNSURI
	38, // 52: tracee.v1beta1.HTTP.headers:type_name -> tracee.v1beta1.HTTP.HeadersEntry
	39, // 53: tracee.v1beta1.HTTPRequest.headers:type_name -> tracee.v1beta1.HTTPRequest.HeadersEntry
	40, // 54: tracee.v1beta1.HTTPResponse.headers:type_name -> tracee.v1beta1.HTTPResponse.HeadersEntry
	2,  // 55: tracee.v1beta1.PacketMetadata.direction:type_name -> tracee.v1beta1.PacketDirection
	34, // 56: tracee.v1beta1.DnsResponseData.dns_query_data:type_name -> tracee.v1beta1.DnsQueryData
	35, // 57: tracee.v1beta1.DnsResponseData.dns_answer:type_name -> tracee.v1beta1.DnsAnswer
	11, // 58: tracee.v1beta1.HookedSeqOps.ValueEntry.value:type_name -> tracee.v1beta1.HookedSymbolData
	32, // 59: tracee.v1beta1.HTTP.HeadersEntry.value:type_name -> tracee.v1beta1.HttpHeader
	32, // 60: tracee.v1beta1.HTTPRequest.HeadersEntry.value:type_name -> tracee.v1beta1.HttpHeader
	32, // 61: tracee.v1beta1.HTTPResponse.HeadersEntry.value:type_name -> tracee.v1beta1.HttpHeader
	62, // [62:62] is the sub-list for method output_type
	62, // [62:62] is the sub-list for method input_type
	62, // [62:62] is the sub-list for extension type_name
	62, // [62:62] is the sub-list for extension extendee
	0,  // [0:62] is the sub-list for field type_name
}

func init() { file_api_v1beta1_event_data_proto_init() }
func file_api_v1beta1_event_data_proto_init() {
	if File_api_v1beta1_event_data_proto != nil {
		return
	}
	file_api_v1beta1_event_data_proto_msgTypes[0].OneofWrappers = []any{
		(*EventValue_Int32)(nil),
		(*EventValue_Int64)(nil),
		(*EventValue_UInt32)(nil),
		(*EventValue_UInt64)(nil),
		(*EventValue_Str)(nil),
		(*EventValue_Bytes)(nil),
		(*EventValue_Bool)(nil),
		(*EventValue_StrArray)(nil),
		(*EventValue_Int32Array)(nil),
		(*EventValue_UInt64Array)(nil),
		(*EventValue_Sockaddr)(nil),
		(*EventValue_Credentials)(nil),
		(*EventValue_Timespec)(nil),
		(*EventValue_HookedSyscalls)(nil),
		(*EventValue_HookedSeqOps)(nil),
		(*EventValue_Ipv4)(nil),
		(*EventValue_Ipv6)(nil),
		(*EventValue_Tcp)(nil),
		(*EventValue_Udp)(nil),
		(*EventValue_Icmp)(nil),
		(*EventValue_Icmpv6)(nil),
		(*EventValue_Dns)(nil),
		(*EventValue_DnsQuestions)(nil),
		(*EventValue_DnsResponses)(nil),
		(*EventValue_PacketMetadata)(nil),
		(*EventValue_Http)(nil),
		(*EventValue_HttpRequest)(nil),
		(*EventValue_HttpResponse)(nil),
		(*EventValue_Struct)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_v1beta1_event_data_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_v1beta1_event_data_proto_goTypes,
		DependencyIndexes: file_api_v1beta1_event_data_proto_depIdxs,
		EnumInfos:         file_api_v1beta1_event_data_proto_enumTypes,
		MessageInfos:      file_api_v1beta1_event_data_proto_msgTypes,
	}.Build()
	File_api_v1beta1_event_data_proto = out.File
	file_api_v1beta1_event_data_proto_rawDesc = nil
	file_api_v1beta1_event_data_proto_goTypes = nil
	file_api_v1beta1_event_data_proto_depIdxs = nil
}

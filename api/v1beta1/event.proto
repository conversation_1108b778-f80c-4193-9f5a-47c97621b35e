syntax = "proto3";

option go_package = "github.co/aquasecurity/tracee/api/v1beta1";

package tracee.v1beta1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "api/v1beta1/event_data.proto";
import "api/v1beta1/threat.proto";

enum EventId {
    // default
    unspecified = 0;

    // Syscalls
    read = 1;
    write = 2;
    open = 3;
    close = 4;
    stat = 5;
    fstat = 6;
    lstat = 7;
    poll = 8;
    lseek = 9;
    mmap = 10;
    mprotect = 11;
    munmap = 12;
    brk = 13;
    rt_sigaction = 14;
    rt_sigprocmask = 15;
    rt_sigreturn = 16;
    ioctl = 17;
    pread64 = 18;
    pwrite64 = 19;
    readv = 20;
    writev = 21;
    access = 22;
    pipe = 23;
    select = 24;
    sched_yield = 25;
    mremap = 26;
    msync = 27;
    mincore = 28;
    madvise = 29;
    shmget = 30;
    shmat = 31;
    shmctl = 32;
    dup = 33;
    dup2 = 34;
    pause = 35;
    nanosleep = 36;
    getitimer = 37;
    alarm = 38;
    setitimer = 39;
    getpid = 40;
    sendfile = 41;
    socket = 42;
    connect = 43;
    accept = 44;
    sendto = 45;
    recvfrom = 46;
    sendmsg = 47;
    recvmsg = 48;
    shutdown = 49;
    bind = 50;
    listen = 51;
    getsockname = 52;
    getpeername = 53;
    socketpair = 54;
    setsockopt = 55;
    getsockopt = 56;
    clone = 57;
    fork = 58;
    vfork = 59;
    execve = 60;
    exit = 61;
    wait4 = 62;
    kill = 63;
    uname = 64;
    semget = 65;
    semop = 66;
    semctl = 67;
    shmdt = 68;
    msgget = 69;
    msgsnd = 70;
    msgrcv = 71;
    msgctl = 72;
    fcntl = 73;
    flock = 74;
    fsync = 75;
    fdatasync = 76;
    truncate = 77;
    ftruncate = 78;
    getdents = 79;
    getcwd = 80;
    chdir = 81;
    fchdir = 82;
    rename = 83;
    mkdir = 84;
    rmdir = 85;
    creat = 86;
    link = 87;
    unlink = 88;
    symlink = 89;
    readlink = 90;
    chmod = 91;
    fchmod = 92;
    chown = 93;
    fchown = 94;
    lchown = 95;
    umask = 96;
    gettimeofday = 97;
    getrlimit = 98;
    getrusage = 99;
    sysinfo = 100;
    times = 101;
    ptrace = 102;
    getuid = 103;
    syslog = 104;
    getgid = 105;
    setuid = 106;
    setgid = 107;
    geteuid = 108;
    getegid = 109;
    setpgid = 110;
    getppid = 111;
    getpgrp = 112;
    setsid = 113;
    setreuid = 114;
    setregid = 115;
    getgroups = 116;
    setgroups = 117;
    setresuid = 118;
    getresuid = 119;
    setresgid = 120;
    getresgid = 121;
    getpgid = 122;
    setfsuid = 123;
    setfsgid = 124;
    getsid = 125;
    capget = 126;
    capset = 127;
    rt_sigpending = 128;
    rt_sigtimedwait = 129;
    rt_sigqueueinfo = 130;
    rt_sigsuspend = 131;
    sigaltstack = 132;
    utime = 133;
    mknod = 134;
    uselib = 135;
    personality = 136;
    ustat = 137;
    statfs = 138;
    fstatfs = 139;
    sysfs = 140;
    getpriority = 141;
    setpriority = 142;
    sched_setparam = 143;
    sched_getparam = 144;
    sched_setscheduler = 145;
    sched_getscheduler = 146;
    sched_get_priority_max = 147;
    sched_get_priority_min = 148;
    sched_rr_get_interval = 149;
    mlock = 150;
    munlock = 151;
    mlockall = 152;
    munlockall = 153;
    vhangup = 154;
    modify_ldt = 155;
    pivot_root = 156;
    sysctl = 157;
    prctl = 158;
    arch_prctl = 159;
    adjtimex = 160;
    setrlimit = 161;
    chroot = 162;
    sync = 163;
    acct = 164;
    settimeofday = 165;
    mount = 166;
    umount2 = 167;
    swapon = 168;
    swapoff = 169;
    reboot = 170;
    sethostname = 171;
    setdomainname = 172;
    iopl = 173;
    ioperm = 174;
    create_module = 175;
    init_module = 176;
    delete_module = 177;
    get_kernel_syms = 178;
    query_module = 179;
    quotactl = 180;
    nfsservctl = 181;
    getpmsg = 182;
    putpmsg = 183;
    afs = 184;
    tuxcall = 185;
    security = 186;
    gettid = 187;
    readahead = 188;
    setxattr = 189;
    lsetxattr = 190;
    fsetxattr = 191;
    getxattr = 192;
    lgetxattr = 193;
    fgetxattr = 194;
    listxattr = 195;
    llistxattr = 196;
    flistxattr = 197;
    removexattr = 198;
    lremovexattr = 199;
    fremovexattr = 200;
    tkill = 201;
    time = 202;
    futex = 203;
    sched_setaffinity = 204;
    sched_getaffinity = 205;
    set_thread_area = 206;
    io_setup = 207;
    io_destroy = 208;
    io_getevents = 209;
    io_submit = 210;
    io_cancel = 211;
    get_thread_area = 212;
    lookup_dcookie = 213;
    epoll_create = 214;
    epoll_ctl_old = 215;
    epoll_wait_old = 216;
    remap_file_pages = 217;
    getdents64 = 218;
    set_tid_address = 219;
    restart_syscall = 220;
    semtimedop = 221;
    fadvise64 = 222;
    timer_create = 223;
    timer_settime = 224;
    timer_gettime = 225;
    timer_getoverrun = 226;
    timer_delete = 227;
    clock_settime = 228;
    clock_gettime = 229;
    clock_getres = 230;
    clock_nanosleep = 231;
    exit_group = 232;
    epoll_wait = 233;
    epoll_ctl = 234;
    tgkill = 235;
    utimes = 236;
    vserver = 237;
    mbind = 238;
    set_mempolicy = 239;
    get_mempolicy = 240;
    mq_open = 241;
    mq_unlink = 242;
    mq_timedsend = 243;
    mq_timedreceive = 244;
    mq_notify = 245;
    mq_getsetattr = 246;
    kexec_load = 247;
    waitid = 248;
    add_key = 249;
    request_key = 250;
    keyctl = 251;
    ioprio_set = 252;
    ioprio_get = 253;
    inotify_init = 254;
    inotify_add_watch = 255;
    inotify_rm_watch = 256;
    migrate_pages = 257;
    openat = 258;
    mkdirat = 259;
    mknodat = 260;
    fchownat = 261;
    futimesat = 262;
    newfstatat = 263;
    unlinkat = 264;
    renameat = 265;
    linkat = 266;
    symlinkat = 267;
    readlinkat = 268;
    fchmodat = 269;
    faccessat = 270;
    pselect6 = 271;
    ppoll = 272;
    unshare = 273;
    set_robust_list = 274;
    get_robust_list = 275;
    splice = 276;
    tee = 277;
    sync_file_range = 278;
    vmsplice = 279;
    move_pages = 280;
    utimensat = 281;
    epoll_pwait = 282;
    signalfd = 283;
    timerfd_create = 284;
    eventfd = 285;
    fallocate = 286;
    timerfd_settime = 287;
    timerfd_gettime = 288;
    accept4 = 289;
    signalfd4 = 290;
    eventfd2 = 291;
    epoll_create1 = 292;
    dup3 = 293;
    pipe2 = 294;
    inotify_init1 = 295;
    preadv = 296;
    pwritev = 297;
    rt_tgsigqueueinfo = 298;
    perf_event_open = 299;
    recvmmsg = 300;
    fanotify_init = 301;
    fanotify_mark = 302;
    prlimit64 = 303;
    name_to_handle_at = 304;
    open_by_handle_at = 305;
    clock_adjtime = 306;
    syncfs = 307;
    sendmmsg = 308;
    setns = 309;
    getcpu = 310;
    process_vm_readv = 311;
    process_vm_writev = 312;
    kcmp = 313;
    finit_module = 314;
    sched_setattr = 315;
    sched_getattr = 316;
    renameat2 = 317;
    seccomp = 318;
    getrandom = 319;
    memfd_create = 320;
    kexec_file_load = 321;
    bpf = 322;
    execveat = 323;
    userfaultfd = 324;
    membarrier = 325;
    mlock2 = 326;
    copy_file_range = 327;
    preadv2 = 328;
    pwritev2 = 329;
    pkey_mprotect = 330;
    pkey_alloc = 331;
    pkey_free = 332;
    statx = 333;
    io_pgetevents = 334;
    rseq = 335;
    pidfd_send_signal = 336;
    io_uring_setup = 337;
    io_uring_enter = 338;
    io_uring_register = 339;
    open_tree = 340;
    move_mount = 341;
    fsopen = 342;
    fsconfig = 343;
    fsmount = 344;
    fspick = 345;
    pidfd_open = 346;
    clone3 = 347;
    close_range = 348;
    openat2 = 349;
    pidfd_getfd = 350;
    faccessat2 = 351;
    process_madvise = 352;
    epoll_pwait2 = 353;
    mount_setattr = 354;
    quotactl_fd = 355;
    landlock_create_ruleset = 356;
    landlock_add_rule = 357;
    landlock_restrict_self = 358;
    memfd_secret = 359;
    process_mrelease = 360;
    waitpid = 361;
    oldfstat = 362;
    break = 363;
    oldstat = 364;
    umount = 365;
    stime = 366;
    stty = 367;
    gtty = 368;
    nice = 369;
    ftime = 370;
    prof = 371;
    signal = 372;
    lock = 373;
    mpx = 374;
    ulimit = 375;
    oldolduname = 376;
    sigaction = 377;
    sgetmask = 378;
    ssetmask = 379;
    sigsuspend = 380;
    sigpending = 381;
    oldlstat = 382;
    readdir = 383;
    profil = 384;
    socketcall = 385;
    olduname = 386;
    idle = 387;
    vm86old = 388;
    ipc = 389;
    sigreturn = 390;
    sigprocmask = 391;
    bdflush = 392;
    afs_syscall = 393;
    llseek = 394;
    old_select = 395;
    vm86 = 396;
    old_getrlimit = 397;
    mmap2 = 398;
    truncate64 = 399;
    ftruncate64 = 400;
    stat64 = 401;
    lstat64 = 402;
    fstat64 = 403;
    lchown16 = 404;
    getuid16 = 405;
    getgid16 = 406;
    geteuid16 = 407;
    getegid16 = 408;
    setreuid16 = 409;
    setregid16 = 410;
    getgroups16 = 411;
    setgroups16 = 412;
    fchown16 = 413;
    setresuid16 = 414;
    getresuid16 = 415;
    setresgid16 = 416;
    getresgid16 = 417;
    chown16 = 418;
    setuid16 = 419;
    setgid16 = 420;
    setfsuid16 = 421;
    setfsgid16 = 422;
    fcntl64 = 423;
    sendfile32 = 424;
    statfs64 = 425;
    fstatfs64 = 426;
    fadvise64_64 = 427;
    clock_gettime32 = 428;
    clock_settime32 = 429;
    clock_adjtime64 = 430;
    clock_getres_time32 = 431;
    clock_nanosleep_time32 = 432;
    timer_gettime32 = 433;
    timer_settime32 = 434;
    timerfd_gettime32 = 435;
    timerfd_settime32 = 436;
    utimensat_time32 = 437;
    pselect6_time32 = 438;
    ppoll_time32 = 439;
    io_pgetevents_time32 = 440;
    recvmmsg_time32 = 441;
    mq_timedsend_time32 = 442;
    mq_timedreceive_time32 = 443;
    rt_sigtimedwait_time32 = 444;
    futex_time32 = 445;
    sched_rr_get_interval_time32 = 446;

    // Common events (used by all architectures).
    net_packet_base = 1000;
    net_packet_ip_base = 1001;
    net_packet_tcp_base = 1002;
    net_packet_udp_base = 1003;
    net_packet_icmp_base = 1004;
    net_packet_icmpv6_base = 1005;
    net_packet_dns_base = 1006;
    net_packet_http_base = 1007;
    net_packet_capture = 1008;
    net_packet_flow = 1009;
    max_net_id = 1010;
    sys_enter = 1011;
    sys_exit = 1012;
    sched_process_fork = 1013;
    sched_process_exec = 1014;
    sched_process_exit = 1015;
    sched_switch = 1016;
    do_exit = 1017;
    cap_capable = 1018;
    vfs_write = 1019;
    vfs_writev = 1020;
    vfs_read = 1021;
    vfs_readv = 1022;
    mem_prot_alert = 1023;
    commit_creds = 1024;
    switch_task_ns = 1025;
    magic_write = 1026;
    cgroup_attach_task = 1027;
    cgroup_mkdir = 1028;
    cgroup_rmdir = 1029;
    security_bprm_check = 1030;
    security_file_open = 1031;
    security_inode_unlink = 1032;
    security_socket_create = 1033;
    security_socket_listen = 1034;
    security_socket_connect = 1035;
    security_socket_accept = 1036;
    security_socket_bind = 1037;
    security_socket_setsockopt = 1038;
    security_sb_mount = 1039;
    security_bpf = 1040;
    security_bpf_map = 1041;
    security_kernel_read_file = 1042;
    security_inode_mknod = 1043;
    security_post_read_file = 1044;
    security_inode_symlink_event_id = 1045;
    security_mmap_file = 1046;
    security_file_mprotect = 1047;
    socket_dup = 1048;
    zeroed_inodes = 1049;
    kernel_write = 1050;
    proc_create = 1051;
    kprobe_attach = 1052;
    call_usermode_helper = 1053;
    dirty_pipe_splice = 1054;
    debugfs_create_file = 1055;
    syscall_table_check = 1056;
    debugfs_create_dir = 1057;
    device_add = 1058;
    register_chrdev = 1059;
    shared_object_loaded = 1060;
    do_init_module = 1061;
    socket_accept = 1062;
    load_elf_phdrs = 1063;
    hooked_proc_fops = 1064;
    print_net_seq_ops = 1065;
    task_rename = 1066;
    security_inode_rename = 1067;
    do_sigaction = 1068;
    bpf_attach = 1069;
    kallsyms_lookup_name = 1070;
    do_mmap = 1071;
    print_mem_dump = 1072;
    vfs_utimes = 1073;
    do_truncate = 1074;
    file_modification = 1075;
    inotify_watch = 1076;
    security_bpf_prog = 1077;
    process_execute_failed = 1078;
    security_path_notify = 1079;
    set_fs_pwd = 1080;
    hidden_kernel_module_seeker = 1081;
    module_load = 1082;
    module_free = 1083;
    execute_finished = 1084;
    process_execute_failed_internal = 1085;
    security_task_set_rlimit = 1086;
    security_settime64 = 1087;
    chmod_common = 1088;
    open_file_ns = 1089;
    open_file_mount = 1090;
    security_task_prctl = 1091;

    // Events originated from user-space
    net_packet_ipv4 = 2000;
    net_packet_ipv6 = 2001;
    net_packet_tcp = 2002;
    net_packet_udp = 2003;
    net_packet_icmp = 2004;
    net_packet_icmpv6 = 2005;
    net_packet_dns = 2006;
    net_packet_dns_request = 2007;
    net_packet_dns_response = 2008;
    net_packet_http = 2009;
    net_packet_http_request = 2010;
    net_packet_http_response = 2011;
    net_flow_end = 2012;
    net_flow_tcp_begin = 2013;
    net_flow_tcp_end = 2014;
    max_user_net_id = 2015;
    net_tcp_connect = 2016;
    init_namespaces = 2017;
    container_create = 2018;
    container_remove = 2019;
    existing_container = 2020;
    hooked_syscall = 2021;
    hooked_seq_ops = 2022;
    symbols_loaded = 2023;
    symbols_collision = 2024;
    hidden_kernel_module = 2025;
    ftrace_hook = 2026;
}

message Event {
    google.protobuf.Timestamp timestamp = 1;
    EventId id = 2;
    string name = 3;
    optional Policies policies = 4;
    Workload workload = 5;
    repeated EventValue data = 6;
    optional Threat threat = 7;
    optional TriggeredBy triggered_by = 8;
}

message Policies {
    repeated string matched = 1;
}

message Workload {
    optional Process process = 1;
    optional Container container = 2;
    optional K8s k8s = 3;
}

message Process  {
    optional Executable executable = 1;
    google.protobuf.UInt32Value unique_id = 2;
    google.protobuf.UInt32Value host_pid = 3;
    google.protobuf.UInt32Value pid = 4;
    optional User real_user = 5;
    optional Thread thread = 6;
    repeated Process ancestors = 7;
}

message Executable {
    string path = 1;
}

message User {
    google.protobuf.UInt32Value id = 1;
}

message Thread {
    google.protobuf.Timestamp start_time = 1;
    string name = 2;
    google.protobuf.UInt32Value unique_id = 3;
    google.protobuf.UInt32Value host_tid = 4;
    google.protobuf.UInt32Value tid = 5;
    string syscall = 6;
    bool compat = 7;
    optional UserStackTrace user_stack_trace = 8;
}

message UserStackTrace {
    repeated StackAddress addresses = 1;
}

message StackAddress {
	uint64 address = 1;
	string symbol = 2;
}

message Container {
    string id = 1;
    string name = 2; 
    ContainerImage image = 3;
    bool is_running = 4;
}

message ContainerImage {
    string id = 1;
    repeated string repo_digests = 2;
    string name = 3;
}

message K8s {
    Pod pod = 1;
    K8sNamespace namespace = 2;
}

message Pod {
    string name = 1;
    string uid = 2;
    map<string, string> labels = 3;
}

message K8sNamespace {
    string name = 1;
}

message TriggeredBy {
    uint32 id = 1;
    string name = 2;
    repeated EventValue data = 3;
}
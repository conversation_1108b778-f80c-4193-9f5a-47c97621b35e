// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.0
// source: api/v1beta1/datasource.proto

package v1beta1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DataSourceService_Write_FullMethodName       = "/tracee.v1beta1.DataSourceService/Write"
	DataSourceService_WriteStream_FullMethodName = "/tracee.v1beta1.DataSourceService/WriteStream"
)

// DataSourceServiceClient is the client API for DataSourceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataSourceServiceClient interface {
	Write(ctx context.Context, in *WriteDataSourceRequest, opts ...grpc.CallOption) (*WriteDataSourceResponse, error)
	WriteStream(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[WriteDataSourceRequest, WriteDataSourceResponse], error)
}

type dataSourceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDataSourceServiceClient(cc grpc.ClientConnInterface) DataSourceServiceClient {
	return &dataSourceServiceClient{cc}
}

func (c *dataSourceServiceClient) Write(ctx context.Context, in *WriteDataSourceRequest, opts ...grpc.CallOption) (*WriteDataSourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WriteDataSourceResponse)
	err := c.cc.Invoke(ctx, DataSourceService_Write_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataSourceServiceClient) WriteStream(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[WriteDataSourceRequest, WriteDataSourceResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &DataSourceService_ServiceDesc.Streams[0], DataSourceService_WriteStream_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[WriteDataSourceRequest, WriteDataSourceResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type DataSourceService_WriteStreamClient = grpc.ClientStreamingClient[WriteDataSourceRequest, WriteDataSourceResponse]

// DataSourceServiceServer is the server API for DataSourceService service.
// All implementations must embed UnimplementedDataSourceServiceServer
// for forward compatibility.
type DataSourceServiceServer interface {
	Write(context.Context, *WriteDataSourceRequest) (*WriteDataSourceResponse, error)
	WriteStream(grpc.ClientStreamingServer[WriteDataSourceRequest, WriteDataSourceResponse]) error
	mustEmbedUnimplementedDataSourceServiceServer()
}

// UnimplementedDataSourceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDataSourceServiceServer struct{}

func (UnimplementedDataSourceServiceServer) Write(context.Context, *WriteDataSourceRequest) (*WriteDataSourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Write not implemented")
}
func (UnimplementedDataSourceServiceServer) WriteStream(grpc.ClientStreamingServer[WriteDataSourceRequest, WriteDataSourceResponse]) error {
	return status.Errorf(codes.Unimplemented, "method WriteStream not implemented")
}
func (UnimplementedDataSourceServiceServer) mustEmbedUnimplementedDataSourceServiceServer() {}
func (UnimplementedDataSourceServiceServer) testEmbeddedByValue()                           {}

// UnsafeDataSourceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataSourceServiceServer will
// result in compilation errors.
type UnsafeDataSourceServiceServer interface {
	mustEmbedUnimplementedDataSourceServiceServer()
}

func RegisterDataSourceServiceServer(s grpc.ServiceRegistrar, srv DataSourceServiceServer) {
	// If the following call panics, it indicates UnimplementedDataSourceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DataSourceService_ServiceDesc, srv)
}

func _DataSourceService_Write_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteDataSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataSourceServiceServer).Write(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataSourceService_Write_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataSourceServiceServer).Write(ctx, req.(*WriteDataSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataSourceService_WriteStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(DataSourceServiceServer).WriteStream(&grpc.GenericServerStream[WriteDataSourceRequest, WriteDataSourceResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type DataSourceService_WriteStreamServer = grpc.ClientStreamingServer[WriteDataSourceRequest, WriteDataSourceResponse]

// DataSourceService_ServiceDesc is the grpc.ServiceDesc for DataSourceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataSourceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "tracee.v1beta1.DataSourceService",
	HandlerType: (*DataSourceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Write",
			Handler:    _DataSourceService_Write_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "WriteStream",
			Handler:       _DataSourceService_WriteStream_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "api/v1beta1/datasource.proto",
}

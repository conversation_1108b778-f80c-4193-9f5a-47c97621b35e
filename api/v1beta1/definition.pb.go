// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.29.0
// source: api/v1beta1/definition.proto

package v1beta1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Version     *Version `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Description string   `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Tags        []string `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty"`
	Threat      *Threat  `protobuf:"bytes,6,opt,name=threat,proto3,oneof" json:"threat,omitempty"`
}

func (x *EventDefinition) Reset() {
	*x = EventDefinition{}
	mi := &file_api_v1beta1_definition_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventDefinition) ProtoMessage() {}

func (x *EventDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_definition_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventDefinition.ProtoReflect.Descriptor instead.
func (*EventDefinition) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_definition_proto_rawDescGZIP(), []int{0}
}

func (x *EventDefinition) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EventDefinition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EventDefinition) GetVersion() *Version {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *EventDefinition) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EventDefinition) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *EventDefinition) GetThreat() *Threat {
	if x != nil {
		return x.Threat
	}
	return nil
}

type Version struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Major uint64 `protobuf:"varint,1,opt,name=major,proto3" json:"major,omitempty"`
	Minor uint64 `protobuf:"varint,2,opt,name=minor,proto3" json:"minor,omitempty"`
	Patch uint64 `protobuf:"varint,3,opt,name=patch,proto3" json:"patch,omitempty"`
}

func (x *Version) Reset() {
	*x = Version{}
	mi := &file_api_v1beta1_definition_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Version) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Version) ProtoMessage() {}

func (x *Version) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_definition_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Version.ProtoReflect.Descriptor instead.
func (*Version) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_definition_proto_rawDescGZIP(), []int{1}
}

func (x *Version) GetMajor() uint64 {
	if x != nil {
		return x.Major
	}
	return 0
}

func (x *Version) GetMinor() uint64 {
	if x != nil {
		return x.Minor
	}
	return 0
}

func (x *Version) GetPatch() uint64 {
	if x != nil {
		return x.Patch
	}
	return 0
}

var File_api_v1beta1_definition_proto protoreflect.FileDescriptor

var file_api_v1beta1_definition_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x1a, 0x18,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x74, 0x68, 0x72, 0x65,
	0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xde, 0x01, 0x0a, 0x0f, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x31, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x33, 0x0a, 0x06, 0x74, 0x68, 0x72,
	0x65, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x61,
	0x74, 0x48, 0x00, 0x52, 0x06, 0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x88, 0x01, 0x01, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x22, 0x4b, 0x0a, 0x07, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69,
	0x6e, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6d, 0x69, 0x6e, 0x6f, 0x72,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x70, 0x61, 0x74, 0x63, 0x68, 0x42, 0x2b, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x2f, 0x61, 0x71, 0x75, 0x61, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x2f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_v1beta1_definition_proto_rawDescOnce sync.Once
	file_api_v1beta1_definition_proto_rawDescData = file_api_v1beta1_definition_proto_rawDesc
)

func file_api_v1beta1_definition_proto_rawDescGZIP() []byte {
	file_api_v1beta1_definition_proto_rawDescOnce.Do(func() {
		file_api_v1beta1_definition_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_v1beta1_definition_proto_rawDescData)
	})
	return file_api_v1beta1_definition_proto_rawDescData
}

var file_api_v1beta1_definition_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_v1beta1_definition_proto_goTypes = []any{
	(*EventDefinition)(nil), // 0: tracee.v1beta1.EventDefinition
	(*Version)(nil),         // 1: tracee.v1beta1.Version
	(*Threat)(nil),          // 2: tracee.v1beta1.Threat
}
var file_api_v1beta1_definition_proto_depIdxs = []int32{
	1, // 0: tracee.v1beta1.EventDefinition.version:type_name -> tracee.v1beta1.Version
	2, // 1: tracee.v1beta1.EventDefinition.threat:type_name -> tracee.v1beta1.Threat
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_v1beta1_definition_proto_init() }
func file_api_v1beta1_definition_proto_init() {
	if File_api_v1beta1_definition_proto != nil {
		return
	}
	file_api_v1beta1_threat_proto_init()
	file_api_v1beta1_definition_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_v1beta1_definition_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_v1beta1_definition_proto_goTypes,
		DependencyIndexes: file_api_v1beta1_definition_proto_depIdxs,
		MessageInfos:      file_api_v1beta1_definition_proto_msgTypes,
	}.Build()
	File_api_v1beta1_definition_proto = out.File
	file_api_v1beta1_definition_proto_rawDesc = nil
	file_api_v1beta1_definition_proto_goTypes = nil
	file_api_v1beta1_definition_proto_depIdxs = nil
}

syntax = "proto3";

option go_package = "github.co/aquasecurity/tracee/api/v1beta1";

package tracee.v1beta1;

import "google/protobuf/wrappers.proto";
import "google/protobuf/struct.proto";

message EventValue {
    string name = 1;
    oneof value {
        int32 int32 = 2; // intT
        int64 int64 = 3; // longT
        uint32 u_int32 = 4; // uintT, modeT, devT, u8T, u16T
        uint64 u_int64 = 5; // ulongT, offT, sizeT, pointerT
        string str = 6; // strT
        bytes bytes = 7; // bytesT
        bool bool = 8; // boolT
        StringArray str_array = 9; // strArrT, argsArrT
        Int32Array int32_array = 10; // intArr2T
        UInt64Array u_int64_array = 11; // uint64ArrT
        SockAddr sockaddr = 12; // sockaddrT
        Credentials credentials = 13; // credT
        Timespec timespec = 14; // timespecT
        HookedSyscalls hooked_syscalls = 15;
        HookedSeqOps hooked_seq_ops = 16;
        IPv4 ipv4 = 17;
        IPv6 ipv6 = 18;
        TCP tcp = 19;
        UDP udp = 20;
        ICMP icmp = 21;
        ICMPv6 icmpv6 = 22;
        DNS dns = 23;
        DnsQuestions dns_questions = 24;
        DnsResponses dns_responses = 25;
        PacketMetadata packet_metadata = 26;
        HTTP http = 27;
        HTTPRequest http_request = 28;
        HTTPResponse http_response = 29;
        google.protobuf.Struct struct = 30;
    }
}

message StringArray {
    repeated string value = 1;
}

message Int32Array {
    repeated int32 value = 1;
}

message UInt64Array {
    repeated uint64 value = 1;
}

message Credentials {
    google.protobuf.UInt32Value uid = 1;
    google.protobuf.UInt32Value gid = 2;
    google.protobuf.UInt32Value suid = 3;
    google.protobuf.UInt32Value sgid = 4;
    google.protobuf.UInt32Value euid = 5;
    google.protobuf.UInt32Value egid = 6;
    google.protobuf.UInt32Value fsuid = 7;
    google.protobuf.UInt32Value fsgid = 8;
    google.protobuf.UInt32Value user_namespace = 9;
    google.protobuf.UInt32Value secure_bits = 10;
    repeated Capability cap_inheritable = 11;
    repeated Capability cap_permitted = 12;
    repeated Capability cap_effective = 13;
    repeated Capability cap_bounding = 14;
    repeated Capability cap_ambient = 15;
}

message Timespec {
    google.protobuf.DoubleValue value = 1;
}

message SockAddr {
    sa_family_t sa_family = 1;
    string sun_path = 2;
    string sin_addr = 3;
    uint32 sin_port = 4;
    string sin6_addr = 5;
    uint32 sin6_port = 6;
    uint32 sin6_flowinfo = 7;
    uint32 sin6_scopeid = 8;
}

enum sa_family_t {
  SA_FAMILY_T_UNSPEC = 0;
  // POSIX.1g used the name AF_LOCAL as a synonym for AF_UNIX,
  // but this name is not used in SUSv3.
  AF_UNIX = 1;
  AF_INET = 2;
  AF_INET6 = 10;
}

// https://pkg.go.dev/kernel.org/pub/linux/libs/security/libcap/cap@v1.2.68#Value
enum Capability {
    CAP_CHOWN = 0;
    DAC_OVERRIDE = 1;
    CAP_DAC_READ_SEARCH = 2;
    CAP_FOWNER = 3;
    CAP_FSETID = 4;
    CAP_KILL = 5;
    CAP_SETGID = 6;
    CAP_SETUID = 7;
    CAP_SETPCAP = 8;
    CAP_NET_BIND_SERVICE = 10;
    CAP_NET_BROADCAST = 11;
    CAP_NET_ADMIN = 12;
    CAP_NET_RAW = 13;
    CAP_IPC_LOCK = 14;
    CAP_IPC_OWNER = 15;
    CAP_SYS_MODULE = 16;
    CAP_SYS_RAWIO = 17;
    CAP_SYS_CHROOT = 18;
    CAP_SYS_PTRACE = 19;
    CAP_SYS_PACCT = 20;
    CAP_SYS_ADMIN = 21;
    CAP_SYS_BOOT = 22;
    CAP_SYS_NICE = 23;
    CAP_SYS_RESOURCE = 24;
    CAP_SYS_TIME = 25;
    CAP_SYS_TTY_CONFIG = 26;
    CAP_MKNOD = 27;
    CAP_LEASE = 28;
    CAP_AUDIT_WRITE = 29;
    CAP_AUDIT_CONTROL = 30;
    CAP_SETFCAP = 31;
    CAP_MAC_OVERRIDE = 32;
    CAP_MAC_ADMIN = 33;
    CAP_SYSLOG = 34;
    CAP_WAKE_ALARM = 35;
    CAP_BLOCK_SUSPEND = 36;
    CAP_AUDIT_READ = 37;
    CAP_PERFMON = 38;
    CAP_BPF = 39;
    CAP_CHECKPOINT_RESTORE = 40;
}

message HookedSyscalls {
    repeated HookedSymbolData value = 1;
}

message HookedSymbolData {
    string symbol_name = 1;
	string module_owner = 2;
}

message HookedSeqOps {
    map<string,HookedSymbolData> value = 1;
}

// network types

message IPv4 {
	uint32 version = 1;
	uint32 ihl = 2;
	uint32 tos = 3;
	uint32 length = 4;
	uint32 id = 5;
	uint32 flags = 6;
	uint32 frag_offset = 7;
	uint32 ttl = 8;
	string protocol = 9;
	uint32 checksum = 10;
	string src_ip = 11;
	string dst_ip = 12;
}

message IPv6 {
	uint32 version = 1;
	uint32 traffic_class = 2;
	uint32 flow_label = 3;
	uint32 length = 4;
	string next_header = 5;
	uint32 hop_limit = 6;
	string src_ip = 7;
	string dst_ip = 8;
}

message TCP {
	uint32 src_port = 1;
	uint32 dst_port = 2;
	uint32 seq = 3;
	uint32 ack = 4;
	uint32 data_offset = 5;
	uint32 fin_flag = 6;
	uint32 syn_flag = 7;
	uint32 rst_flag = 8;
	uint32 psh_flag = 9;
	uint32 ack_flag = 10;
	uint32 urg_flag = 11;
	uint32 ece_flag = 12;
	uint32 cwr_flag = 13;
	uint32 ns_flag = 14;
	uint32 window = 15;
	uint32 checksum = 16;
	uint32 urgent = 17;
}

message UDP {
	uint32 src_port = 1;
	uint32 dst_port = 2;
	uint32 length = 3; 
	uint32 checksum = 4;
}

message ICMP {
	string type_code = 1;
	uint32 checksum = 2;
	uint32 id = 3;
	uint32 seq = 4;
}

message ICMPv6 {
	string type_code = 1;
	uint32 checksum = 2;
}

message DnsQuestions {
    repeated DnsQueryData questions = 1;
}

message DnsResponses {
    repeated DnsResponseData responses = 1;
}

message DNS {
	uint32 id = 1;
	uint32 qr = 2;
	string op_code = 3;
	uint32 aa = 4;
	uint32 tc = 5;
	uint32 rd = 6;
	uint32 ra = 7;
	uint32 z = 8;
	string response_code = 9;
	uint32 qd_count = 10;
	uint32 an_count = 11;
	uint32 ns_count = 12;
	uint32 ar_count = 13;
	repeated DNSQuestion questions = 14;
	repeated DNSResourceRecord answers = 15;
	repeated DNSResourceRecord authorities = 16;
	repeated DNSResourceRecord additionals = 17;
}

message DNSQuestion {
	string name = 1;
	string type = 2;
	string class = 3;
}

message DNSResourceRecord {
	string name = 1;
	string type = 2;
	string class = 3;
	uint32 ttl = 4;
	string ip = 5;
	string ns = 6;
	string cname = 7;
	string ptr = 8; 
	repeated string txts = 9;
	DNSSOA soa = 10;
	DNSSRV srv = 11;
	DNSMX mx = 12;
	repeated DNSOPT opt = 13;
	DNSURI uri = 14;
	string txt = 15;
}

message DNSSOA {
	string mname = 1;
	string rname = 2;
	uint32 serial = 3;
	uint32 refresh = 4;
	uint32 retry = 5;
	uint32 expire = 6;
	uint32 minimum = 7;
}

message DNSSRV {
	uint32 priority = 1;
	uint32 weight = 2;
	uint32 port = 3;
	string name = 4;
}

message DNSMX {
	uint32 Preference = 1;
	string name = 2;
}

message DNSURI {
	uint32 priority = 1;
	uint32 weight = 2;
	string target = 3;
}

message DNSOPT {
	string code = 1;
	string data = 2;
}

message HTTP {
	string direction = 1;
	string method = 2;
	string protocol = 3;
	string host = 4;
	string uri_path = 5;
	string status = 6;
	int32 status_code = 7;
	map<string, HttpHeader> headers = 8;
	int64 content_length = 9;
}

message HTTPRequest {
	string method = 1;
	string protocol = 3;
	string host = 4; 
	string uri_path = 5;
	map<string, HttpHeader> headers = 6;
	int64 content_length = 7;
}

message HTTPResponse {
	string status = 1;
	int32 status_code = 2;
	string protocol = 3;
	map<string, HttpHeader> headers = 4;
	int64 content_length = 5;
}

message HttpHeader {
    repeated string header = 1;
}

enum PacketDirection {
	INVALID = 0;
	INGRESS = 1;
	EGRESS = 3;
}

message PacketMetadata {
	string src_ip = 1;
	string dst_ip = 2;
	uint32 src_port = 3;
	uint32 dst_port = 4;
	uint32 protocol = 5;
	uint32 packet_len = 6;
	string iface = 7;
	PacketDirection direction = 8;
}

message DnsQueryData {
	string query = 1;
	string query_type = 2;
	string query_class = 3;
}

message DnsAnswer {
	string type = 1;
	uint32 ttl = 2;
	string answer = 3;
}

message DnsResponseData {
	DnsQueryData dns_query_data = 1;
	repeated DnsAnswer dns_answer = 2;
}
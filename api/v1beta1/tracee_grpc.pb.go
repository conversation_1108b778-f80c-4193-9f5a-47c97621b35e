// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.0
// source: api/v1beta1/tracee.proto

package v1beta1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TraceeService_GetEventDefinitions_FullMethodName = "/tracee.v1beta1.TraceeService/GetEventDefinitions"
	TraceeService_StreamEvents_FullMethodName        = "/tracee.v1beta1.TraceeService/StreamEvents"
	TraceeService_EnableEvent_FullMethodName         = "/tracee.v1beta1.TraceeService/EnableEvent"
	TraceeService_DisableEvent_FullMethodName        = "/tracee.v1beta1.TraceeService/DisableEvent"
	TraceeService_GetVersion_FullMethodName          = "/tracee.v1beta1.TraceeService/GetVersion"
)

// TraceeServiceClient is the client API for TraceeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TraceeServiceClient interface {
	GetEventDefinitions(ctx context.Context, in *GetEventDefinitionsRequest, opts ...grpc.CallOption) (*GetEventDefinitionsResponse, error)
	StreamEvents(ctx context.Context, in *StreamEventsRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamEventsResponse], error)
	EnableEvent(ctx context.Context, in *EnableEventRequest, opts ...grpc.CallOption) (*EnableEventResponse, error)
	DisableEvent(ctx context.Context, in *DisableEventRequest, opts ...grpc.CallOption) (*DisableEventResponse, error)
	GetVersion(ctx context.Context, in *GetVersionRequest, opts ...grpc.CallOption) (*GetVersionResponse, error)
}

type traceeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTraceeServiceClient(cc grpc.ClientConnInterface) TraceeServiceClient {
	return &traceeServiceClient{cc}
}

func (c *traceeServiceClient) GetEventDefinitions(ctx context.Context, in *GetEventDefinitionsRequest, opts ...grpc.CallOption) (*GetEventDefinitionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEventDefinitionsResponse)
	err := c.cc.Invoke(ctx, TraceeService_GetEventDefinitions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *traceeServiceClient) StreamEvents(ctx context.Context, in *StreamEventsRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[StreamEventsResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &TraceeService_ServiceDesc.Streams[0], TraceeService_StreamEvents_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[StreamEventsRequest, StreamEventsResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type TraceeService_StreamEventsClient = grpc.ServerStreamingClient[StreamEventsResponse]

func (c *traceeServiceClient) EnableEvent(ctx context.Context, in *EnableEventRequest, opts ...grpc.CallOption) (*EnableEventResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EnableEventResponse)
	err := c.cc.Invoke(ctx, TraceeService_EnableEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *traceeServiceClient) DisableEvent(ctx context.Context, in *DisableEventRequest, opts ...grpc.CallOption) (*DisableEventResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DisableEventResponse)
	err := c.cc.Invoke(ctx, TraceeService_DisableEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *traceeServiceClient) GetVersion(ctx context.Context, in *GetVersionRequest, opts ...grpc.CallOption) (*GetVersionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVersionResponse)
	err := c.cc.Invoke(ctx, TraceeService_GetVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TraceeServiceServer is the server API for TraceeService service.
// All implementations must embed UnimplementedTraceeServiceServer
// for forward compatibility.
type TraceeServiceServer interface {
	GetEventDefinitions(context.Context, *GetEventDefinitionsRequest) (*GetEventDefinitionsResponse, error)
	StreamEvents(*StreamEventsRequest, grpc.ServerStreamingServer[StreamEventsResponse]) error
	EnableEvent(context.Context, *EnableEventRequest) (*EnableEventResponse, error)
	DisableEvent(context.Context, *DisableEventRequest) (*DisableEventResponse, error)
	GetVersion(context.Context, *GetVersionRequest) (*GetVersionResponse, error)
	mustEmbedUnimplementedTraceeServiceServer()
}

// UnimplementedTraceeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTraceeServiceServer struct{}

func (UnimplementedTraceeServiceServer) GetEventDefinitions(context.Context, *GetEventDefinitionsRequest) (*GetEventDefinitionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEventDefinitions not implemented")
}
func (UnimplementedTraceeServiceServer) StreamEvents(*StreamEventsRequest, grpc.ServerStreamingServer[StreamEventsResponse]) error {
	return status.Errorf(codes.Unimplemented, "method StreamEvents not implemented")
}
func (UnimplementedTraceeServiceServer) EnableEvent(context.Context, *EnableEventRequest) (*EnableEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnableEvent not implemented")
}
func (UnimplementedTraceeServiceServer) DisableEvent(context.Context, *DisableEventRequest) (*DisableEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableEvent not implemented")
}
func (UnimplementedTraceeServiceServer) GetVersion(context.Context, *GetVersionRequest) (*GetVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersion not implemented")
}
func (UnimplementedTraceeServiceServer) mustEmbedUnimplementedTraceeServiceServer() {}
func (UnimplementedTraceeServiceServer) testEmbeddedByValue()                       {}

// UnsafeTraceeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TraceeServiceServer will
// result in compilation errors.
type UnsafeTraceeServiceServer interface {
	mustEmbedUnimplementedTraceeServiceServer()
}

func RegisterTraceeServiceServer(s grpc.ServiceRegistrar, srv TraceeServiceServer) {
	// If the following call panics, it indicates UnimplementedTraceeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TraceeService_ServiceDesc, srv)
}

func _TraceeService_GetEventDefinitions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEventDefinitionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TraceeServiceServer).GetEventDefinitions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TraceeService_GetEventDefinitions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TraceeServiceServer).GetEventDefinitions(ctx, req.(*GetEventDefinitionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TraceeService_StreamEvents_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(StreamEventsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TraceeServiceServer).StreamEvents(m, &grpc.GenericServerStream[StreamEventsRequest, StreamEventsResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type TraceeService_StreamEventsServer = grpc.ServerStreamingServer[StreamEventsResponse]

func _TraceeService_EnableEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TraceeServiceServer).EnableEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TraceeService_EnableEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TraceeServiceServer).EnableEvent(ctx, req.(*EnableEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TraceeService_DisableEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TraceeServiceServer).DisableEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TraceeService_DisableEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TraceeServiceServer).DisableEvent(ctx, req.(*DisableEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TraceeService_GetVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TraceeServiceServer).GetVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TraceeService_GetVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TraceeServiceServer).GetVersion(ctx, req.(*GetVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TraceeService_ServiceDesc is the grpc.ServiceDesc for TraceeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TraceeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "tracee.v1beta1.TraceeService",
	HandlerType: (*TraceeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEventDefinitions",
			Handler:    _TraceeService_GetEventDefinitions_Handler,
		},
		{
			MethodName: "EnableEvent",
			Handler:    _TraceeService_EnableEvent_Handler,
		},
		{
			MethodName: "DisableEvent",
			Handler:    _TraceeService_DisableEvent_Handler,
		},
		{
			MethodName: "GetVersion",
			Handler:    _TraceeService_GetVersion_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamEvents",
			Handler:       _TraceeService_StreamEvents_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "api/v1beta1/tracee.proto",
}

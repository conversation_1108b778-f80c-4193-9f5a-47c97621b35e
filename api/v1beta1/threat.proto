syntax = "proto3";

option go_package = "github.co/aquasecurity/tracee/api/v1beta1";

package tracee.v1beta1;

enum Severity {
    INFO = 0;
    LOW = 1;
    MEDIUM = 2;
    HIGH = 3;
    CRITICAL = 4;
}

message Threat {
    string description = 1;
    Mitre mitre = 2;
    Severity severity = 3;
    string name = 4;
    map<string,string> properties = 5;
}

message Mitre {
    MitreTactic tactic = 1;
    MitreTechnique technique = 2;
}

message MitreTactic {
    string name = 1;
}

message MitreTechnique {
    string id = 1;
    string name = 2;
}
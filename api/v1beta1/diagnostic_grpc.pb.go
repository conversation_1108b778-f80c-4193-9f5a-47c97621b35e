// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.0
// source: api/v1beta1/diagnostic.proto

package v1beta1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DiagnosticService_GetMetrics_FullMethodName     = "/tracee.v1beta1.DiagnosticService/GetMetrics"
	DiagnosticService_ChangeLogLevel_FullMethodName = "/tracee.v1beta1.DiagnosticService/ChangeLogLevel"
	DiagnosticService_GetStacktrace_FullMethodName  = "/tracee.v1beta1.DiagnosticService/GetStacktrace"
)

// DiagnosticServiceClient is the client API for DiagnosticService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DiagnosticServiceClient interface {
	GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error)
	ChangeLogLevel(ctx context.Context, in *ChangeLogLevelRequest, opts ...grpc.CallOption) (*ChangeLogLevelResponse, error)
	GetStacktrace(ctx context.Context, in *GetStacktraceRequest, opts ...grpc.CallOption) (*GetStacktraceResponse, error)
}

type diagnosticServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDiagnosticServiceClient(cc grpc.ClientConnInterface) DiagnosticServiceClient {
	return &diagnosticServiceClient{cc}
}

func (c *diagnosticServiceClient) GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMetricsResponse)
	err := c.cc.Invoke(ctx, DiagnosticService_GetMetrics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *diagnosticServiceClient) ChangeLogLevel(ctx context.Context, in *ChangeLogLevelRequest, opts ...grpc.CallOption) (*ChangeLogLevelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangeLogLevelResponse)
	err := c.cc.Invoke(ctx, DiagnosticService_ChangeLogLevel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *diagnosticServiceClient) GetStacktrace(ctx context.Context, in *GetStacktraceRequest, opts ...grpc.CallOption) (*GetStacktraceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetStacktraceResponse)
	err := c.cc.Invoke(ctx, DiagnosticService_GetStacktrace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DiagnosticServiceServer is the server API for DiagnosticService service.
// All implementations must embed UnimplementedDiagnosticServiceServer
// for forward compatibility.
type DiagnosticServiceServer interface {
	GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error)
	ChangeLogLevel(context.Context, *ChangeLogLevelRequest) (*ChangeLogLevelResponse, error)
	GetStacktrace(context.Context, *GetStacktraceRequest) (*GetStacktraceResponse, error)
	mustEmbedUnimplementedDiagnosticServiceServer()
}

// UnimplementedDiagnosticServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDiagnosticServiceServer struct{}

func (UnimplementedDiagnosticServiceServer) GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetrics not implemented")
}
func (UnimplementedDiagnosticServiceServer) ChangeLogLevel(context.Context, *ChangeLogLevelRequest) (*ChangeLogLevelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeLogLevel not implemented")
}
func (UnimplementedDiagnosticServiceServer) GetStacktrace(context.Context, *GetStacktraceRequest) (*GetStacktraceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStacktrace not implemented")
}
func (UnimplementedDiagnosticServiceServer) mustEmbedUnimplementedDiagnosticServiceServer() {}
func (UnimplementedDiagnosticServiceServer) testEmbeddedByValue()                           {}

// UnsafeDiagnosticServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DiagnosticServiceServer will
// result in compilation errors.
type UnsafeDiagnosticServiceServer interface {
	mustEmbedUnimplementedDiagnosticServiceServer()
}

func RegisterDiagnosticServiceServer(s grpc.ServiceRegistrar, srv DiagnosticServiceServer) {
	// If the following call panics, it indicates UnimplementedDiagnosticServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DiagnosticService_ServiceDesc, srv)
}

func _DiagnosticService_GetMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiagnosticServiceServer).GetMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiagnosticService_GetMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiagnosticServiceServer).GetMetrics(ctx, req.(*GetMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiagnosticService_ChangeLogLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeLogLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiagnosticServiceServer).ChangeLogLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiagnosticService_ChangeLogLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiagnosticServiceServer).ChangeLogLevel(ctx, req.(*ChangeLogLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiagnosticService_GetStacktrace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStacktraceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiagnosticServiceServer).GetStacktrace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiagnosticService_GetStacktrace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiagnosticServiceServer).GetStacktrace(ctx, req.(*GetStacktraceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DiagnosticService_ServiceDesc is the grpc.ServiceDesc for DiagnosticService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DiagnosticService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "tracee.v1beta1.DiagnosticService",
	HandlerType: (*DiagnosticServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMetrics",
			Handler:    _DiagnosticService_GetMetrics_Handler,
		},
		{
			MethodName: "ChangeLogLevel",
			Handler:    _DiagnosticService_ChangeLogLevel_Handler,
		},
		{
			MethodName: "GetStacktrace",
			Handler:    _DiagnosticService_GetStacktrace_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/v1beta1/diagnostic.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.29.0
// source: api/v1beta1/datasource.proto

package v1beta1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WriteDataSourceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Namespace string          `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Key       *structpb.Value `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	Value     *structpb.Value `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *WriteDataSourceRequest) Reset() {
	*x = WriteDataSourceRequest{}
	mi := &file_api_v1beta1_datasource_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WriteDataSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteDataSourceRequest) ProtoMessage() {}

func (x *WriteDataSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_datasource_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteDataSourceRequest.ProtoReflect.Descriptor instead.
func (*WriteDataSourceRequest) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_datasource_proto_rawDescGZIP(), []int{0}
}

func (x *WriteDataSourceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WriteDataSourceRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *WriteDataSourceRequest) GetKey() *structpb.Value {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *WriteDataSourceRequest) GetValue() *structpb.Value {
	if x != nil {
		return x.Value
	}
	return nil
}

type WriteDataSourceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WriteDataSourceResponse) Reset() {
	*x = WriteDataSourceResponse{}
	mi := &file_api_v1beta1_datasource_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WriteDataSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteDataSourceResponse) ProtoMessage() {}

func (x *WriteDataSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_v1beta1_datasource_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteDataSourceResponse.ProtoReflect.Descriptor instead.
func (*WriteDataSourceResponse) Descriptor() ([]byte, []int) {
	return file_api_v1beta1_datasource_proto_rawDescGZIP(), []int{1}
}

var File_api_v1beta1_datasource_proto protoreflect.FileDescriptor

var file_api_v1beta1_datasource_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x01, 0x0a,
	0x16, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x19, 0x0a,
	0x17, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xcf, 0x01, 0x0a, 0x11, 0x44, 0x61, 0x74,
	0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x58,
	0x0a, 0x05, 0x57, 0x72, 0x69, 0x74, 0x65, 0x12, 0x26, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x60, 0x0a, 0x0b, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x26, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x28, 0x01, 0x42, 0x2b, 0x5a, 0x29, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x2f, 0x61, 0x71, 0x75, 0x61, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_v1beta1_datasource_proto_rawDescOnce sync.Once
	file_api_v1beta1_datasource_proto_rawDescData = file_api_v1beta1_datasource_proto_rawDesc
)

func file_api_v1beta1_datasource_proto_rawDescGZIP() []byte {
	file_api_v1beta1_datasource_proto_rawDescOnce.Do(func() {
		file_api_v1beta1_datasource_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_v1beta1_datasource_proto_rawDescData)
	})
	return file_api_v1beta1_datasource_proto_rawDescData
}

var file_api_v1beta1_datasource_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_v1beta1_datasource_proto_goTypes = []any{
	(*WriteDataSourceRequest)(nil),  // 0: tracee.v1beta1.WriteDataSourceRequest
	(*WriteDataSourceResponse)(nil), // 1: tracee.v1beta1.WriteDataSourceResponse
	(*structpb.Value)(nil),          // 2: google.protobuf.Value
}
var file_api_v1beta1_datasource_proto_depIdxs = []int32{
	2, // 0: tracee.v1beta1.WriteDataSourceRequest.key:type_name -> google.protobuf.Value
	2, // 1: tracee.v1beta1.WriteDataSourceRequest.value:type_name -> google.protobuf.Value
	0, // 2: tracee.v1beta1.DataSourceService.Write:input_type -> tracee.v1beta1.WriteDataSourceRequest
	0, // 3: tracee.v1beta1.DataSourceService.WriteStream:input_type -> tracee.v1beta1.WriteDataSourceRequest
	1, // 4: tracee.v1beta1.DataSourceService.Write:output_type -> tracee.v1beta1.WriteDataSourceResponse
	1, // 5: tracee.v1beta1.DataSourceService.WriteStream:output_type -> tracee.v1beta1.WriteDataSourceResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_v1beta1_datasource_proto_init() }
func file_api_v1beta1_datasource_proto_init() {
	if File_api_v1beta1_datasource_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_v1beta1_datasource_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_v1beta1_datasource_proto_goTypes,
		DependencyIndexes: file_api_v1beta1_datasource_proto_depIdxs,
		MessageInfos:      file_api_v1beta1_datasource_proto_msgTypes,
	}.Build()
	File_api_v1beta1_datasource_proto = out.File
	file_api_v1beta1_datasource_proto_rawDesc = nil
	file_api_v1beta1_datasource_proto_goTypes = nil
	file_api_v1beta1_datasource_proto_depIdxs = nil
}

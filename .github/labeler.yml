# Define labels for areas and kinds with match objects

#
# Area Labels
#
area/grpc:
  - changed-files:
      - any-glob-to-any-file:
          - pkg/server/grpc/**
          - pkg/server/grpc/**/*

area/api:
  - changed-files:
      - any-glob-to-any-file:
        - api/**
        - api/**/*

area/build:
  - changed-files:
      - any-glob-to-any-file:
        - builder/**
        - '**/Makefile'
        - '**/Dockerfile'
        - go.sum
        - go.mod
        - staticcheck.conf
        - .github/**
        - .clang-format
        - .dockerignore

area/capture:
  - changed-files:
      - any-glob-to-any-file:
        - '*capture*'

area/ebpf:
  - changed-files:
      - any-glob-to-any-file:
        - pkg/ebpf/*
        - pkg/ebpf/**/*

area/events:
  - changed-files:
      - any-glob-to-any-file:
        - pkg/events/*
        - pkg/events/**/*

area/filtering:
  - changed-files:
      - any-glob-to-any-file:
        - pkg/filters/*
        - pkg/filters/**/*

area/flags:
  - changed-files:
      - any-glob-to-any-file:
        - pkg/cmd/flags/*
        - pkg/cmd/flags/**/*

area/kubernetes:
  - changed-files:
      - any-glob-to-any-file:
        - deploy/*
        - deploy/**/*

area/logging:
  - changed-files:
      - any-glob-to-any-file:
        - pkg/logger/*
        - pkg/logger/**/*
        - pkg/errfmt/*
        - pkg/errfmt/**/*

area/performance:
  - changed-files:
      - any-glob-to-any-file:
        - pkg/metrics/*
        - pkg/metrics/**/*

area/release:
  - changed-files:
      - any-glob-to-any-file:
        - packaging/*
        - packaging/**/*

area/signatures:
  - changed-files:
      - any-glob-to-any-file:
        - pkg/signatures/*
        - pkg/signatures/**/*
        - signatures/*
        - signatures/**/*

area/testing:
  - changed-files:
      - any-glob-to-any-file:
        - tests/*
        - tests/**/*
        - '**/*_test.go'

area/UX:
  - changed-files:
      - any-glob-to-any-file:
        - pkg/cmd/*
        - pkg/cmd/**/*
        - cmd/*
        - cmd/**/*

#
# Kind Labels
#

kind/documentation:
  - changed-files:
      - any-glob-to-any-file:
        - docs/*
        - docs/**/*
        - '**/*.md'
        - mkdocs.yml
        - NOTICE
        - LICENSE
        - cmd/tracee-gptdocs/*

# kind/bug:
# kind/chore:
# kind/feature:

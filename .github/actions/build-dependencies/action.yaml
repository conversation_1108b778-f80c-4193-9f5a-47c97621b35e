name: Build Dependencies
description: |
  Install build dependencies to test and compile tracee artifacts
runs:
  using: composite
  steps:
    - name: Install Base Packages
      run: |
        sudo apt-get update
        sudo apt-get install -y bsdutils build-essential pkgconf
        sudo apt-get install -y zlib1g-dev libelf-dev libzstd-dev
        sudo apt-get install -y software-properties-common
      shell: bash
    - name: Install Golang
      run: |
        sudo rm -f /usr/bin/go
        sudo rm -f /usr/bin/gofmt
        curl -L -o /tmp/golang.tar.gz https://go.dev/dl/go1.24.0.linux-amd64.tar.gz
        sudo tar -C /usr/local -xzf /tmp/golang.tar.gz
        sudo ln -s /usr/local/go/bin/go /usr/bin/go
        sudo ln -s /usr/local/go/bin/gofmt /usr/bin/gofmt
      shell: bash
    - name: Install Clang Format 12
      run: |
        sudo rm -f /usr/bin/clang-format-12
        curl -L -o /tmp/clang-format-12 https://github.com/muttleyxd/clang-tools-static-binaries/releases/download/master-f4f85437/clang-format-12.0.1_linux-amd64
        sudo mv /tmp/clang-format-12 /usr/bin/clang-format-12
        sudo chmod 755 /usr/bin/clang-format-12
      shell: bash
    - name: Install Clang
      run: |
        sudo rm -f /usr/bin/cc
        sudo rm -f /usr/bin/clang
        sudo rm -f /usr/bin/clang++
        sudo rm -f /usr/bin/llc
        sudo rm -f /usr/bin/lld
        sudo rm -f /usr/bin/clangd
        sudo rm -f /usr/bin/clang-format
        sudo rm -f /usr/bin/llvm-strip
        sudo rm -f /usr/bin/llvm-config
        sudo rm -f /usr/bin/ld.lld
        sudo rm -f /usr/bin/llvm-ar
        sudo rm -f /usr/bin/llvm-nm
        sudo rm -f /usr/bin/llvm-objcopy
        sudo rm -f /usr/bin/llvm-objdump
        sudo rm -f /usr/bin/llvm-readelf
        sudo rm -f /usr/bin/opt
        curl -L -o /tmp/clang.tar.xz https://github.com/llvm/llvm-project/releases/download/llvmorg-14.0.6/clang+llvm-14.0.6-x86_64-linux-gnu-rhel-8.4.tar.xz
        sudo tar -C /usr/local -xJf /tmp/clang.tar.xz
        sudo mv "/usr/local/clang+llvm-14.0.6-x86_64-linux-gnu-rhel-8.4" /usr/local/clang
        sudo ln -s /usr/local/clang/bin/clang /usr/bin/clang
        sudo ln -s /usr/local/clang/bin/clang++ /usr/bin/clang++
        sudo ln -s /usr/local/clang/bin/clangd /usr/bin/clangd
        sudo ln -s /usr/local/clang/bin/clang-format /usr/bin/clang-format
        sudo ln -s /usr/local/clang/bin/lld /usr/bin/lld
        sudo ln -s /usr/local/clang/bin/llc /usr/bin/llc
        sudo ln -s /usr/local/clang/bin/llvm-strip /usr/bin/llvm-strip
        sudo ln -s /usr/local/clang/bin/llvm-config /usr/bin/llvm-config
        sudo ln -s /usr/local/clang/bin/ld.lld /usr/bin/ld.lld
        sudo ln -s /usr/local/clang/bin/llvm-ar /usr/bin/llvm-ar
        sudo ln -s /usr/local/clang/bin/llvm-nm /usr/bin/llvm-nm
        sudo ln -s /usr/local/clang/bin/llvm-objcopy /usr/bin/llvm-objcopy
        sudo ln -s /usr/local/clang/bin/llvm-objdump /usr/bin/llvm-objdump
        sudo ln -s /usr/local/clang/bin/llvm-readelf /usr/bin/llvm-readelf
        sudo ln -s /usr/local/clang/bin/opt /usr/bin/opt
      shell: bash
    - name: Install staticchecker
      run: |
        GOROOT=/usr/local/go GOPATH=$HOME/go go install honnef.co/go/tools/cmd/staticcheck@2025.1
        sudo cp $HOME/go/bin/staticcheck /usr/bin/
      shell: bash
    - name: Install revive
      run: |
        GOROOT=/usr/local/go GOPATH=$HOME/go go install github.com/mgechev/revive@v1.7.0
        sudo cp $HOME/go/bin/revive /usr/bin/
      shell: bash
    - name: Install goimports-reviser
      run: |
        GOROOT=/usr/local/go GOPATH=$HOME/go go install github.com/incu6us/goimports-reviser/v3@v3.8.2
        sudo cp $HOME/go/bin/goimports-reviser /usr/bin/
      shell: bash
    - name: Install errcheck
      run: |
        GOROOT=/usr/local/go GOPATH=$HOME/go go install github.com/kisielk/errcheck@v1.9.0
        sudo cp $HOME/go/bin/errcheck /usr/bin/
      shell: bash
    - name: Install docker
      run: |
        sudo apt-get install --yes ca-certificates curl gnupg lsb-release
        sudo mkdir -p /etc/apt/keyrings
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
        echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
        sudo apt-get --yes update
        sudo apt-get install --yes docker-ce docker-ce-cli containerd.io
      shell: bash

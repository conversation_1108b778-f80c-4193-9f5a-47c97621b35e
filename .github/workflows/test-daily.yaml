#
# Daily Test: Test latest libbpfgo
#
name: Libbpfgo Test
on:
  workflow_dispatch: {}
  schedule:
    # Daily At 03:00
    - cron: "0 3 * * *"
  workflow_call:
jobs:
  compile-tracee:
    name: Test Latest libbpfgo
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          submodules: true
      - name: Install Dependencies
        uses: ./.github/actions/build-dependencies
      - name: Compile Tracee
        run: |
          make test-upstream-libbpfgo

name: Test Helm Charts

on:
  pull_request:
    paths:
      - "deploy/helm/**"

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          fetch-depth: 0

      - name: Set up Helm
        uses: azure/setup-helm@b9e51907a09c216f16ebe8536097933489208112 # v4.3.0
        with:
          version: v3.14.3

      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.12.3

      - name: Set up chart-testing
        uses: helm/chart-testing-action@0d28d3144d3a25ea2cc349d6e59901c4ff469b3b # v2.7.0

      - name: Run chart-testing (lint)
        run: ct lint --config deploy/helm/ct.yaml --lint-conf  deploy/helm/lintconf.yaml --chart-yaml-schema deploy/helm/chart_schema.yaml

      - name: <PERSON><PERSON> KIND Cluster
        uses: helm/kind-action@a1b0e391336a6ee6713a0583f8c6240d70863de3 # v1.12.0

      - name: Run chart-testing (install)
        run: ct install --config deploy/helm/ct.yaml

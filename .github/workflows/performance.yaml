# #
# # When a PR is opened or updated which touches eBPF: run performance pipeline
# #
# name: Performance Pipeline
# on:
#   workflow_dispatch: {}
#   push:
#     branches:
#       - main
#     paths:
#       - "pkg/ebpf/c/**.c"
#       - "pkg/ebpf/c/**.h"
#       - "**/performance.yaml"
# permissions:
#   id-token: write
#   contents: read
# jobs:
#   publish-pr-image:
#     name: Publish PR Image
#     runs-on: [self-hosted, perf]
#     steps:
#       - name: AWS Authentication
#         uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502 # v4.0.2
#         env:
#           AWS_PROFILE: ${{ secrets.AWS_PROFILE }}
#         with:
#           aws-region: ${{ secrets.AWS_REGION}}
#           role-to-assume: ${{ secrets.AWS_RUNNER_ROLE_ARN }}
#       - name: Checkout Code
#         uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
#       - name: Build Image and Publish
#         env:
#           AWS_REGION: ${{ secrets.AWS_REGION }}
#           REPO_USER: ${{ secrets.AWS_PR_IMAGE_ECR_REPO_USER }} 
#           REPO: ${{ secrets.AWS_ECR_NAME }}
#           PR_REPO: ${{ secrets.AWS_PR_IMAGE_ECR_REPO }}
#         run: |
#           make -f builder/Makefile.tracee-container build-tracee
#           aws ecr get-login-password --region $AWS_REGION | docker login --username $REPO_USER --password-stdin $REPO
#           docker tag tracee:latest $PR_REPO:$GITHUB_SHA
#           docker push $PR_REPO:$GITHUB_SHA
#           docker logout $REPO
#           docker rmi tracee:latest $PR_REPO:$GITHUB_SHA
#   network-benchmark:
#     name: Network Performance Gate
#     needs:
#       - publish-pr-image
#     runs-on: [self-hosted, perf]
#     steps:
#       - name: AWS Authentication
#         uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502 # v4.0.2
#         env:
#           AWS_PROFILE: ${{ secrets.AWS_PROFILE }}
#         with:
#           aws-region: ${{ secrets.AWS_REGION}}
#           role-to-assume: ${{ secrets.AWS_RUNNER_ROLE_ARN }}
#       - name: Checkout Code
#         uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
#       - name: Run Benchmark
#         env:
#           PR_REPO: ${{ secrets.AWS_PR_IMAGE_ECR_REPO }}
#           AWS_REGION: ${{ secrets.AWS_REGION }}
#           PERF_CLUSTER: ${{ secrets.PERF_CLUSTER_NAME }}
#         # 1. Get kubeconfig
#         # 2. Move working dir to the benchmark directory
#         # 3. Run the benchmark
#         # 4. Move back to the base repository path
#         run: |
#           aws eks update-kubeconfig --region $AWS_REGION --name $PERF_CLUSTER
#           make bench-network IMAGE=$PR_REPO:$GITHUB_SHA OUTPUT=bench_output.json
#       - name: Store benchmark result
#         uses: benchmark-action/github-action-benchmark@1846227a307d8c0149b960b986d46f8f4c95db0c #v1.20.1
#         with:
#           # What benchmark tool the output.txt came from
#           tool: 'customSmallerIsBetter'
#           # Where the output from the benchmark tool is stored
#           output-file-path: bench_output.json
#           # Workflow will fail when an alert happens
#           fail-on-alert: false
#           # Tag user when alerted
#           alert-comment-cc-users: '@aquasecurity/tracee'
#           # Percentage threshold to trigger an alert. For example, if our last result was 1.4, 
#           # if the next result exceeds X% of 1.4, there will be an alert.
#           alert-threshold: '10%'
#       - name: Cleanup Benchmark Result
#         run: |
#           rm bench_output.json

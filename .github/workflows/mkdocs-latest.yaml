#
# On demand and on vXXX tag push: Deploy the latest documentatio
#
name: Deploy the latest documentation
on:
  workflow_dispatch:
    inputs:
      ref:
        description: The tag to be released, e.g. v0.0.1
        required: true
jobs:
  deploy:
    name: Deploy the latest documentation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout main
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: ${{ github.event.inputs.ref }}
          fetch-depth: 0
          persist-credentials: true
      - name: Login to docker.io registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.12.3
      - name: Install dependencies
        run: |
          pip install git+https://${GH_TOKEN}@github.com/squidfunk/mkdocs-material-insiders.git
          pip install mike
          pip install mkdocs-macros-plugin
        env:
          GH_TOKEN: ${{ secrets.MKDOCS_AQUA_BOT }}
      - name: Setup Git
        run: |
          git config user.name "github-actions"
          git config user.email "<EMAIL>"
      - name: Deploy the latest documents
        run: |
          VERSION="${{ github.event.inputs.ref }}"
          mike deploy --push --update-aliases ${VERSION%.*} latest

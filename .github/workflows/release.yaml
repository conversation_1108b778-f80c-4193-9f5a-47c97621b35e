#
# When tag vXXX is pushed: Release Tracee
#
name: Release
on:
  workflow_dispatch:
    inputs:
      ref:
        description: The tag to be released, e.g. v0.0.1
        required: true
jobs:
  release-x86_64:
    name: Release (x86_64)
    env:
      GH_TOKEN: ${{ github.token }}
    runs-on: 
      - graas_ami-0cdf7ad6d9627da45_${{ github.event.number }}${{ github.run_attempt }}-${{ github.run_id }}
      - EXECUTION_TYPE=LONG
      - INSTANCE_TYPE=2XLARGE
    permissions:
      contents: write
      packages: write
      id-token: write
    steps:
      - name: Checkout Code
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: ${{ github.event.inputs.ref }}
          submodules: true
          fetch-depth: 0
      - name: Install Cosign
        uses: sigstore/cosign-installer@3454372f43399081ed03b604cb2d021dabca52bb # v3.8.2
        with:
          cosign-release: 'v2.2.4'
      - name: Login to docker.io registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build
        run: |
          make -f builder/Makefile.release release
        shell: bash
      - name: Publish to docker.io registry
        run: |
          TAG=$(echo ${{ github.event.inputs.ref }} | sed -e "s/v//gI")
          ARCH=$(uname -m)
          docker image tag tracee:latest aquasec/tracee:${ARCH}-${TAG}
          docker image push aquasec/tracee:${ARCH}-${TAG}
        shell: bash
      - name: Sign Docker image
        run: |
          TAG=$(echo ${{ github.event.inputs.ref }} | sed -e "s/v//gI")
          ARCH=$(uname -m)
          cosign sign -y $(docker inspect --format='{{index .RepoDigests 0}}' aquasec/tracee:${ARCH}-${TAG})
        shell: bash
  release-aarch64:
    name: Release (aarch64)
    env:
      GH_TOKEN: ${{ github.token }}
    runs-on:
      - graas_ami-07740487fa433aa54_${{ github.event.number }}${{ github.run_attempt }}-${{ github.run_id }}
      - EXECUTION_TYPE=LONG
      - INSTANCE_TYPE=LARGE
    permissions:
      contents: write
      packages: write
      id-token: write
    steps:
      - name: Checkout Code
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: ${{ github.event.inputs.ref }}
          submodules: true
          fetch-depth: 0
      - name: Install Cosign
        uses: sigstore/cosign-installer@3454372f43399081ed03b604cb2d021dabca52bb # v3.8.2
        with:
          cosign-release: 'v2.2.4'
      - name: Login to docker.io registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build
        run: |
          make -f builder/Makefile.release release
        shell: bash
      - name: Publish to docker.io registry
        run: |
          TAG=$(echo ${{ github.event.inputs.ref }} | sed -e "s/v//gI")
          ARCH=$(uname -m)
          docker image tag tracee:latest aquasec/tracee:${ARCH}-${TAG}
          docker image push aquasec/tracee:${ARCH}-${TAG}
        shell: bash
      - name: Sign Docker image
        run: |
          TAG=$(echo ${{ github.event.inputs.ref }} | sed -e "s/v//gI")
          ARCH=$(uname -m)
          cosign sign -y $(docker inspect --format='{{index .RepoDigests 0}}' aquasec/tracee:${ARCH}-${TAG})
        shell: bash
  release:
    name: Release
    needs: [release-x86_64, release-aarch64]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      id-token: write
    steps:
      - name: Checkout Code
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          ref: ${{ github.event.inputs.ref }}
          submodules: true
          fetch-depth: 0
      - name: Install Cosign
        uses: sigstore/cosign-installer@3454372f43399081ed03b604cb2d021dabca52bb # v3.8.2
        with:
          cosign-release: 'v2.2.4'
      - name: Login to docker.io registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Publish to docker.io registry
        run: |
          TAG=$(echo ${{ github.event.inputs.ref }} | sed -e "s/v//gI")
          export DOCKER_CLI_EXPERIMENTAL=enabled
          docker manifest create aquasec/tracee:latest \
            aquasec/tracee:x86_64-${TAG} \
            aquasec/tracee:aarch64-${TAG}
          docker manifest create aquasec/tracee:${TAG} \
            aquasec/tracee:x86_64-${TAG} \
            aquasec/tracee:aarch64-${TAG}
          docker manifest push aquasec/tracee:latest
          docker manifest push aquasec/tracee:${TAG}
        shell: bash
      - name: Sign the latest manifest with Cosign
        run: |
          TAG=$(echo ${{ github.event.inputs.ref }} | sed -e "s/v//gI")
          cosign sign -y aquasec/tracee:latest
          cosign sign -y aquasec/tracee:${TAG}
        shell: bash

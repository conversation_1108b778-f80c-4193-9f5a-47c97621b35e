#
# On document changes and on demand: Deploy the dev documentatio
#
name: Deploy the dev documentation
on:
  workflow_dispatch: {}
  push:
    paths:
      - "docs/**"
      - mkdocs.yml
    branches:
      - main
jobs:
  deploy:
    name: Deploy the dev documentation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout main
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          fetch-depth: 0
          persist-credentials: true
      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.12.3
      - name: Install dependencies
        run: |
          pip install git+https://${GH_TOKEN}@github.com/squidfunk/mkdocs-material-insiders.git
          pip install mike
          pip install mkdocs-macros-plugin
        env:
          GH_TOKEN: ${{ secrets.MKDOCS_AQUA_BOT }}
      - name: Setup Git
        run: |
          git config user.name "github-actions"
          git config user.email "<EMAIL>"
      - name: Deploy the dev documents
        run: mike deploy --push dev

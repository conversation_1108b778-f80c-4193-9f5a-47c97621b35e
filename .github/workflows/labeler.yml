#
# When a PR is opened or updated: Label it based on files being changed
#
name: "Labeler"
on:
  - pull_request_target
jobs:
  triage:
    name: <PERSON><PERSON> Pull Request
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9 # v5.0.0
        with:
          sync-labels: false
          configuration-path: .github/labeler.yml

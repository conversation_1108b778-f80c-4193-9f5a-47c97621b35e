#
# On cron schedule or on demand: Release snapshot
#
# This workflow ensures that the main branch is ready for release and that all
# build configuration files are valid. Also scans tracee container images for
# vulnerabilities, and publishes to DockerHub as aquasec/tracee:dev.
#
name: Release Snapshot
on:
  workflow_dispatch: {}
  schedule:
    # Daily at 05:00
    - cron: "0 5 * * *"
jobs:
  release-snapshot-x86_64:
    name: Release Snapshot (x86_64)
    runs-on: 
      - graas_ami-0cdf7ad6d9627da45_${{ github.event.number }}${{ github.run_attempt }}-${{ github.run_id }}
      - EXECUTION_TYPE=LONG
    permissions:
      contents: read
      packages: write
      id-token: write
    steps:
      - name: Checkout Code
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          submodules: true
          fetch-depth: 0
      # - name: Install Cosign
      #   uses: sigstore/cosign-installer@main
      #   with:
      #     cosign-release: 'v2.2.4'
      - name: Login to docker.io registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build
        run: |
          make -f builder/Makefile.release snapshot
      - name: Scan Docker Image for Vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: "tracee:dev"
          severity: "CRITICAL"
          exit-code: "1"
      - name: Publish to docker.io registry
        run: |
          docker image tag tracee:dev aquasec/tracee:x86_64-dev
          docker image push aquasec/tracee:x86_64-dev
        shell: bash
      # Disabled to avoid generating too many sigstore cosign signatures
      # - name: Sign Docker image
      #   run: |
      #     cosign sign -y $(docker inspect --format='{{index .RepoDigests 0}}' aquasec/tracee:x86_64-dev)
      #   shell: bash
  release-snapshot-aarch64:
    name: Release Snapshot (aarch64)
    runs-on: 
      - graas_ami-07740487fa433aa54_${{ github.event.number }}${{ github.run_attempt }}-${{ github.run_id }}
      - EXECUTION_TYPE=LONG
    permissions:
      contents: read
      packages: write
      id-token: write
    steps:
      - name: Checkout Code
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          submodules: true
          fetch-depth: 0
      # - name: Install Cosign
      #   uses: sigstore/cosign-installer@main
      #   with:
      #     cosign-release: 'v2.2.4'
      - name: Login to docker.io registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build
        run: |
          make -f builder/Makefile.release snapshot
      - name: Scan Docker Image for Vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: "tracee:dev"
          severity: "CRITICAL"
          exit-code: "1"
      - name: Publish to docker.io registry
        run: |
          docker image tag tracee:dev aquasec/tracee:aarch64-dev
          docker image push aquasec/tracee:aarch64-dev
        shell: bash
      # Disabled to avoid generating too many sigstore cosign signatures
      # - name: Sign Docker image
      #   run: |
      #     cosign sign -y $(docker inspect --format='{{index .RepoDigests 0}}' aquasec/tracee:aarch64-dev)
      #   shell: bash
  release-snapshot:
    name: Release Snapshot
    needs: [release-snapshot-x86_64, release-snapshot-aarch64]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      id-token: write
    steps:
      - name: Checkout Code
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          submodules: true
          fetch-depth: 0
      # - name: Install Cosign
      #   uses: sigstore/cosign-installer@main
      #   with:
      #     cosign-release: 'v2.2.4'
      - name: Login to docker.io registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USER }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Publish to docker.io registry
        run: |
          export DOCKER_CLI_EXPERIMENTAL=enabled
          docker manifest create aquasec/tracee:dev \
            aquasec/tracee:x86_64-dev \
            aquasec/tracee:aarch64-dev
          docker manifest push aquasec/tracee:dev

          timestamp=$(date +%Y%m%d-%H%M%S%Z)
          docker manifest create aquasec/tracee:dev-$timestamp \
            aquasec/tracee:x86_64-dev \
            aquasec/tracee:aarch64-dev
          docker manifest push aquasec/tracee:dev-$timestamp
        shell: bash
      # Disabled to avoid generating too many sigstore cosign signatures
      # - name: Sign Docker image
      #   run: |
      #     cosign sign -y aquasec/tracee:dev
      #   shell: bash
